#!/usr/bin/env node

/**
 * Comprehensive Automated Test Suite for Cloud Functions Architecture
 * Tests all components: libraries, functions, API routes, and integration
 */

const { spawn, exec } = require('child_process');
const fs = require('fs');
const path = require('path');
const { promisify } = require('util');

const execAsync = promisify(exec);

// Test configuration
const TEST_CONFIG = {
  timeout: 30000, // 30 seconds per test
  retries: 3,
  emulatorPorts: {
    functions: 5001,
    firestore: 8080,
    pubsub: 8085,
    ui: 4000
  },
  webappPort: 3000
};

// Test results tracking
let testResults = {
  passed: 0,
  failed: 0,
  skipped: 0,
  errors: []
};

// Utility functions
const sleep = (ms) => new Promise(resolve => setTimeout(resolve, ms));

const logTest = (testName, status, details = '') => {
  const timestamp = new Date().toISOString();
  const statusIcon = status === 'PASS' ? '✅' : status === 'FAIL' ? '❌' : status === 'SKIP' ? '⏭️' : '🔄';
  console.log(`${statusIcon} [${timestamp}] ${testName}: ${status}`);
  if (details) console.log(`   ${details}`);
  
  if (status === 'PASS') testResults.passed++;
  else if (status === 'FAIL') {
    testResults.failed++;
    testResults.errors.push({ test: testName, details });
  } else if (status === 'SKIP') testResults.skipped++;
};

const runCommand = async (command, cwd = process.cwd()) => {
  try {
    const { stdout, stderr } = await execAsync(command, { cwd, timeout: TEST_CONFIG.timeout });
    return { success: true, stdout, stderr };
  } catch (error) {
    return { success: false, error: error.message, stdout: error.stdout, stderr: error.stderr };
  }
};

const checkPort = async (port) => {
  try {
    const response = await fetch(`http://localhost:${port}`, { 
      method: 'GET',
      signal: AbortSignal.timeout(5000)
    });
    return response.ok || response.status < 500;
  } catch (error) {
    return false;
  }
};

// Test Suite Classes
class BuildTests {
  static async runAll() {
    console.log('\n🔨 Running Build Tests...');
    
    await this.testSharedLibraryBuild();
    await this.testEmailCoreLibraryBuild();
    await this.testEmailAnalysisFunctionBuild();
    await this.testNotificationHandlerBuild();
    await this.testWebappBuild();
  }

  static async testSharedLibraryBuild() {
    const result = await runCommand('npx nx build shared');
    if (result.success) {
      logTest('Shared Library Build', 'PASS');
    } else {
      logTest('Shared Library Build', 'FAIL', result.error);
    }
  }

  static async testEmailCoreLibraryBuild() {
    const result = await runCommand('npx nx build email-core');
    if (result.success) {
      logTest('Email Core Library Build', 'PASS');
    } else {
      logTest('Email Core Library Build', 'FAIL', result.error);
    }
  }

  static async testEmailAnalysisFunctionBuild() {
    const result = await runCommand('npx nx build email-analysis-function');
    if (result.success) {
      logTest('Email Analysis Function Build', 'PASS');
    } else {
      logTest('Email Analysis Function Build', 'FAIL', result.error);
    }
  }

  static async testNotificationHandlerBuild() {
    const result = await runCommand('npx nx build notification-handler-function');
    if (result.success) {
      logTest('Notification Handler Function Build', 'PASS');
    } else {
      logTest('Notification Handler Function Build', 'FAIL', result.error);
    }
  }

  static async testWebappBuild() {
    const result = await runCommand('npx nx build webapp');
    if (result.success) {
      logTest('Webapp Build', 'PASS');
    } else {
      logTest('Webapp Build', 'FAIL', 'Expected - has known TypeScript issues');
    }
  }
}

class LibraryTests {
  static async runAll() {
    console.log('\n📚 Running Library Tests...');
    
    await this.testSharedLibraryExports();
    await this.testEmailCoreExports();
    await this.testLibraryImports();
  }

  static async testSharedLibraryExports() {
    try {
      const indexPath = path.join(process.cwd(), 'dist/libs/shared/src/index.js');
      if (fs.existsSync(indexPath)) {
        const sharedLib = require(indexPath);
        const expectedExports = ['Logger', 'Database', 'sanitizeId', 'sanitizeEmail'];
        const hasAllExports = expectedExports.every(exp => sharedLib[exp] !== undefined);
        
        if (hasAllExports) {
          logTest('Shared Library Exports', 'PASS');
        } else {
          logTest('Shared Library Exports', 'FAIL', 'Missing expected exports');
        }
      } else {
        logTest('Shared Library Exports', 'FAIL', 'Build output not found');
      }
    } catch (error) {
      logTest('Shared Library Exports', 'FAIL', error.message);
    }
  }

  static async testEmailCoreExports() {
    try {
      const indexPath = path.join(process.cwd(), 'dist/libs/email-core/src/index.js');
      if (fs.existsSync(indexPath)) {
        const emailCore = require(indexPath);
        const expectedExports = ['EmailAnalyzer', 'OpenAIService'];
        const hasAllExports = expectedExports.every(exp => emailCore[exp] !== undefined);
        
        if (hasAllExports) {
          logTest('Email Core Library Exports', 'PASS');
        } else {
          logTest('Email Core Library Exports', 'FAIL', 'Missing expected exports');
        }
      } else {
        logTest('Email Core Library Exports', 'FAIL', 'Build output not found');
      }
    } catch (error) {
      logTest('Email Core Library Exports', 'FAIL', error.message);
    }
  }

  static async testLibraryImports() {
    try {
      // Test that Cloud Functions can import libraries
      const emailFunctionPath = path.join(process.cwd(), 'dist/apps/email-analysis-function/main.js');
      if (fs.existsSync(emailFunctionPath)) {
        const functionCode = fs.readFileSync(emailFunctionPath, 'utf8');
        const hasImports = functionCode.includes('EmailAnalyzer') || functionCode.includes('email-core');
        
        if (hasImports) {
          logTest('Library Import Integration', 'PASS');
        } else {
          logTest('Library Import Integration', 'FAIL', 'Functions not importing libraries');
        }
      } else {
        logTest('Library Import Integration', 'FAIL', 'Function build output not found');
      }
    } catch (error) {
      logTest('Library Import Integration', 'FAIL', error.message);
    }
  }
}

class EmulatorTests {
  static async runAll() {
    console.log('\n🔥 Running Emulator Tests...');
    
    await this.testEmulatorConnectivity();
    await this.testFirestoreEmulator();
    await this.testPubSubEmulator();
    await this.testFunctionsEmulator();
  }

  static async testEmulatorConnectivity() {
    const ports = Object.values(TEST_CONFIG.emulatorPorts);
    let allConnected = true;
    
    for (const port of ports) {
      const isConnected = await checkPort(port);
      if (!isConnected) {
        allConnected = false;
        logTest(`Emulator Port ${port}`, 'FAIL', 'Not accessible');
      }
    }
    
    if (allConnected) {
      logTest('Emulator Connectivity', 'PASS');
    } else {
      logTest('Emulator Connectivity', 'FAIL', 'Some emulators not accessible');
    }
  }

  static async testFirestoreEmulator() {
    try {
      const response = await fetch(`http://localhost:${TEST_CONFIG.emulatorPorts.firestore}`, {
        signal: AbortSignal.timeout(5000)
      });
      
      if (response.status === 200 || response.status === 404) {
        logTest('Firestore Emulator', 'PASS');
      } else {
        logTest('Firestore Emulator', 'FAIL', `Unexpected status: ${response.status}`);
      }
    } catch (error) {
      logTest('Firestore Emulator', 'FAIL', error.message);
    }
  }

  static async testPubSubEmulator() {
    try {
      // Test Pub/Sub emulator by checking if it responds
      const response = await fetch(`http://localhost:${TEST_CONFIG.emulatorPorts.pubsub}`, {
        signal: AbortSignal.timeout(5000)
      });
      
      // Pub/Sub emulator might return different status codes
      if (response.status < 500) {
        logTest('Pub/Sub Emulator', 'PASS');
      } else {
        logTest('Pub/Sub Emulator', 'FAIL', `Status: ${response.status}`);
      }
    } catch (error) {
      logTest('Pub/Sub Emulator', 'FAIL', error.message);
    }
  }

  static async testFunctionsEmulator() {
    try {
      const response = await fetch(`http://localhost:${TEST_CONFIG.emulatorPorts.functions}`, {
        signal: AbortSignal.timeout(5000)
      });
      
      if (response.status === 200 || response.status === 404) {
        logTest('Functions Emulator', 'PASS');
      } else {
        logTest('Functions Emulator', 'FAIL', `Status: ${response.status}`);
      }
    } catch (error) {
      logTest('Functions Emulator', 'FAIL', error.message);
    }
  }
}

class FunctionTests {
  static async runAll() {
    console.log('\n☁️ Running Cloud Function Tests...');
    
    await this.testEmailAnalysisFunctionDeployment();
    await this.testNotificationHandlerDeployment();
    await this.testPubSubMessageFlow();
    await this.testWebhookIntegration();
  }

  static async testEmailAnalysisFunctionDeployment() {
    try {
      // Check if function is deployed to emulator
      const response = await fetch(
        `http://localhost:${TEST_CONFIG.emulatorPorts.functions}/ddjs-dev-458016/us-central1/analyzeEmail`,
        { signal: AbortSignal.timeout(5000) }
      );
      
      // Function might return 405 for GET requests, which is expected
      if (response.status === 405 || response.status === 200) {
        logTest('Email Analysis Function Deployment', 'PASS');
      } else {
        logTest('Email Analysis Function Deployment', 'FAIL', `Status: ${response.status}`);
      }
    } catch (error) {
      logTest('Email Analysis Function Deployment', 'FAIL', error.message);
    }
  }

  static async testNotificationHandlerDeployment() {
    try {
      const response = await fetch(
        `http://localhost:${TEST_CONFIG.emulatorPorts.functions}/ddjs-dev-458016/us-central1/handleGmailNotification`,
        { 
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ test: true }),
          signal: AbortSignal.timeout(5000)
        }
      );
      
      // Any response indicates the function is deployed
      if (response.status < 500) {
        logTest('Notification Handler Deployment', 'PASS');
      } else {
        logTest('Notification Handler Deployment', 'FAIL', `Status: ${response.status}`);
      }
    } catch (error) {
      logTest('Notification Handler Deployment', 'FAIL', error.message);
    }
  }

  static async testPubSubMessageFlow() {
    try {
      // This test requires the Pub/Sub emulator and functions to be running
      // We'll test by checking if we can create a topic
      const { PubSub } = require('@google-cloud/pubsub');
      
      process.env.PUBSUB_EMULATOR_HOST = `localhost:${TEST_CONFIG.emulatorPorts.pubsub}`;
      
      const pubsub = new PubSub({ projectId: 'ddjs-dev-458016' });
      const topicName = 'test-topic-' + Date.now();
      
      const [topic] = await pubsub.topic(topicName).get({ autoCreate: true });
      await topic.delete();
      
      logTest('Pub/Sub Message Flow', 'PASS');
    } catch (error) {
      logTest('Pub/Sub Message Flow', 'FAIL', error.message);
    }
  }

  static async testWebhookIntegration() {
    // This test checks if the webhook endpoint exists
    try {
      const response = await fetch(`http://localhost:${TEST_CONFIG.webappPort}/api/emails/progress-webhook`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          clerkUserId: 'test',
          messageId: 'test',
          status: 'processing'
        }),
        signal: AbortSignal.timeout(5000)
      });
      
      if (response.status === 200 || response.status === 400) {
        logTest('Webhook Integration', 'PASS');
      } else {
        logTest('Webhook Integration', 'FAIL', `Status: ${response.status}`);
      }
    } catch (error) {
      logTest('Webhook Integration', 'FAIL', error.message);
    }
  }
}

class IntegrationTests {
  static async runAll() {
    console.log('\n🔗 Running Integration Tests...');
    
    await this.testEndToEndEmailAnalysis();
    await this.testAPIRouteIntegration();
    await this.testRealTimeUpdates();
  }

  static async testEndToEndEmailAnalysis() {
    try {
      // Test the full flow: API -> Pub/Sub -> Function -> Webhook
      const testData = {
        startDate: '2024-01-01',
        endDate: '2024-01-02'
      };
      
      const response = await fetch(`http://localhost:${TEST_CONFIG.webappPort}/api/emails/analyze`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(testData),
        signal: AbortSignal.timeout(10000)
      });
      
      if (response.status === 200 || response.status === 401) {
        // 401 is expected without proper authentication
        logTest('End-to-End Email Analysis', 'PASS', 'API endpoint accessible');
      } else {
        logTest('End-to-End Email Analysis', 'FAIL', `Status: ${response.status}`);
      }
    } catch (error) {
      logTest('End-to-End Email Analysis', 'FAIL', error.message);
    }
  }

  static async testAPIRouteIntegration() {
    try {
      // Test estimate API route
      const response = await fetch(`http://localhost:${TEST_CONFIG.webappPort}/api/emails/estimate?startDate=2024-01-01&endDate=2024-01-02`, {
        signal: AbortSignal.timeout(5000)
      });
      
      if (response.status === 200 || response.status === 401) {
        logTest('API Route Integration', 'PASS');
      } else {
        logTest('API Route Integration', 'FAIL', `Status: ${response.status}`);
      }
    } catch (error) {
      logTest('API Route Integration', 'FAIL', error.message);
    }
  }

  static async testRealTimeUpdates() {
    try {
      // Test polling-based analysis runs endpoint
      const response = await fetch(`http://localhost:${TEST_CONFIG.webappPort}/api/analysis-runs`, {
        signal: AbortSignal.timeout(5000)
      });

      // Polling endpoint should return 401 (auth required) or 200 (if authenticated)
      if (response.status === 401 || response.status === 200) {
        logTest('Polling-based Real-Time Updates', 'PASS', `Status: ${response.status}`);
      } else {
        logTest('Polling-based Real-Time Updates', 'FAIL', `Unexpected status: ${response.status}`);
      }
    } catch (error) {
      logTest('Polling-based Real-Time Updates', 'FAIL', error.message);
    }
  }
}

// Main test runner
class TestRunner {
  static async runAllTests() {
    console.log('🧪 Starting Automated Test Suite for Cloud Functions Architecture');
    console.log('================================================================');
    
    const startTime = Date.now();
    
    try {
      // Run all test categories
      await BuildTests.runAll();
      await LibraryTests.runAll();
      await EmulatorTests.runAll();
      await FunctionTests.runAll();
      await IntegrationTests.runAll();
      
    } catch (error) {
      console.error('❌ Test suite encountered an error:', error);
    }
    
    const endTime = Date.now();
    const duration = ((endTime - startTime) / 1000).toFixed(2);
    
    // Print summary
    console.log('\n📊 Test Results Summary');
    console.log('=======================');
    console.log(`✅ Passed: ${testResults.passed}`);
    console.log(`❌ Failed: ${testResults.failed}`);
    console.log(`⏭️ Skipped: ${testResults.skipped}`);
    console.log(`⏱️ Duration: ${duration}s`);
    
    if (testResults.failed > 0) {
      console.log('\n❌ Failed Tests:');
      testResults.errors.forEach(error => {
        console.log(`   • ${error.test}: ${error.details}`);
      });
    }
    
    const successRate = ((testResults.passed / (testResults.passed + testResults.failed)) * 100).toFixed(1);
    console.log(`\n📈 Success Rate: ${successRate}%`);
    
    if (testResults.failed === 0) {
      console.log('\n🎉 All tests passed! The Cloud Functions architecture is working correctly.');
    } else {
      console.log('\n⚠️ Some tests failed. Please review the errors above.');
    }
    
    return testResults.failed === 0;
  }
}

// Export for use as module
module.exports = { TestRunner, BuildTests, LibraryTests, EmulatorTests, FunctionTests, IntegrationTests };

// Run if called directly
if (require.main === module) {
  TestRunner.runAllTests()
    .then(success => process.exit(success ? 0 : 1))
    .catch(error => {
      console.error('Fatal error:', error);
      process.exit(1);
    });
}
