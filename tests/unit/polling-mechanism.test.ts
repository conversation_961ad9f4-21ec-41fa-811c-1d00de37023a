/**
 * Unit tests for polling-based real-time updates
 * Tests the adaptive polling mechanism and job status updates
 */

import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';

// Mock the useRealTimeUpdates hook
const mockUseRealTimeUpdates = {
  activeRunIds: ['job1', 'job2'],
  onUpdate: jest.fn(),
  interval: 5000,
  enabled: true,
  adaptivePolling: true,
  maxInterval: 30000,
  minInterval: 2000
};

// Mock fetch for API calls
global.fetch = jest.fn();

describe('Polling Mechanism', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    jest.useFakeTimers();
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  describe('Adaptive Polling Intervals', () => {
    it('should start with default interval', () => {
      const { interval } = mockUseRealTimeUpdates;
      expect(interval).toBe(5000);
    });

    it('should adjust interval based on activity', () => {
      // Simulate active processing
      const activeInterval = 3000; // 3 seconds for active jobs
      const idleInterval = 15000;  // 15 seconds for idle jobs
      
      expect(activeInterval).toBeLessThan(idleInterval);
      expect(activeInterval).toBeGreaterThanOrEqual(2000); // Min interval
      expect(idleInterval).toBeLessThanOrEqual(30000); // Max interval
    });

    it('should increase interval on consecutive errors', () => {
      const baseInterval = 5000;
      const errorMultiplier = 1.5;
      const maxInterval = 30000;
      
      let currentInterval = baseInterval;
      
      // Simulate 3 consecutive errors
      for (let i = 0; i < 3; i++) {
        currentInterval = Math.min(currentInterval * errorMultiplier, maxInterval);
      }
      
      expect(currentInterval).toBeGreaterThan(baseInterval);
      expect(currentInterval).toBeLessThanOrEqual(maxInterval);
    });

    it('should decrease interval on successful responses', () => {
      const baseInterval = 15000; // Start with higher interval
      const successMultiplier = 0.8;
      const minInterval = 2000;
      
      const newInterval = Math.max(baseInterval * successMultiplier, minInterval);
      
      expect(newInterval).toBeLessThan(baseInterval);
      expect(newInterval).toBeGreaterThanOrEqual(minInterval);
    });
  });

  describe('API Polling', () => {
    it('should poll analysis runs endpoint', async () => {
      const mockResponse = {
        runs: [
          {
            id: 'job1',
            status: 'processing',
            progress: { processed: 50, total: 100 }
          }
        ],
        total: 1,
        hasMore: false
      };

      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse
      });

      const response = await fetch('/api/analysis-runs');
      const data = await response.json();

      expect(fetch).toHaveBeenCalledWith('/api/analysis-runs');
      expect(data).toEqual(mockResponse);
      expect(data.runs).toHaveLength(1);
      expect(data.runs[0].status).toBe('processing');
    });

    it('should handle polling errors gracefully', async () => {
      (global.fetch as jest.Mock).mockRejectedValueOnce(new Error('Network error'));

      try {
        await fetch('/api/analysis-runs');
      } catch (error) {
        expect(error).toBeInstanceOf(Error);
        expect((error as Error).message).toBe('Network error');
      }
    });

    it('should poll individual job status', async () => {
      const jobId = 'test-job-123';
      const mockJobResponse = {
        job: {
          id: jobId,
          status: 'completed',
          progress: { processed: 100, total: 100 }
        }
      };

      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockJobResponse
      });

      const response = await fetch(`/api/jobs/${jobId}`);
      const data = await response.json();

      expect(fetch).toHaveBeenCalledWith(`/api/jobs/${jobId}`);
      expect(data.job.status).toBe('completed');
      expect(data.job.progress.processed).toBe(100);
    });
  });

  describe('Polling Performance', () => {
    it('should not exceed maximum polling frequency', () => {
      const minInterval = 2000; // 2 seconds minimum
      const maxRequestsPerMinute = 60000 / minInterval; // 30 requests per minute max
      
      expect(maxRequestsPerMinute).toBeLessThanOrEqual(30);
    });

    it('should not be too slow for user experience', () => {
      const maxInterval = 30000; // 30 seconds maximum
      const minRequestsPerMinute = 60000 / maxInterval; // 2 requests per minute min
      
      expect(minRequestsPerMinute).toBeGreaterThanOrEqual(2);
    });

    it('should batch multiple job status requests', async () => {
      const jobIds = ['job1', 'job2', 'job3'];
      const mockBatchResponse = {
        jobs: jobIds.map(id => ({
          id,
          status: 'processing',
          progress: { processed: 50, total: 100 }
        }))
      };

      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockBatchResponse
      });

      // Simulate batch request
      const response = await fetch('/api/jobs/batch-status', {
        method: 'POST',
        body: JSON.stringify({ jobIds })
      });
      const data = await response.json();

      expect(fetch).toHaveBeenCalledWith('/api/jobs/batch-status', {
        method: 'POST',
        body: JSON.stringify({ jobIds })
      });
      expect(data.jobs).toHaveLength(3);
    });
  });

  describe('Error Handling', () => {
    it('should handle 401 authentication errors', async () => {
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: false,
        status: 401,
        json: async () => ({ message: 'Unauthorized' })
      });

      const response = await fetch('/api/analysis-runs');
      expect(response.status).toBe(401);
    });

    it('should handle 429 rate limiting errors', async () => {
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: false,
        status: 429,
        headers: new Map([
          ['Retry-After', '60'],
          ['X-RateLimit-Reset', '1642680000']
        ]),
        json: async () => ({ 
          message: 'Rate limit exceeded',
          retryAfter: 60
        })
      });

      const response = await fetch('/api/analysis-runs');
      expect(response.status).toBe(429);
    });

    it('should handle network timeouts', async () => {
      const timeoutError = new Error('Request timeout');
      (global.fetch as jest.Mock).mockRejectedValueOnce(timeoutError);

      try {
        await fetch('/api/analysis-runs');
      } catch (error) {
        expect(error).toBe(timeoutError);
      }
    });
  });

  describe('Cleanup and Memory Management', () => {
    it('should clear polling intervals on component unmount', () => {
      const clearIntervalSpy = jest.spyOn(global, 'clearInterval');
      const intervalId = setInterval(() => {}, 1000);
      
      // Simulate component unmount
      clearInterval(intervalId);
      
      expect(clearIntervalSpy).toHaveBeenCalledWith(intervalId);
    });

    it('should not create memory leaks with multiple polling instances', () => {
      const intervals: NodeJS.Timeout[] = [];
      
      // Create multiple polling instances
      for (let i = 0; i < 5; i++) {
        intervals.push(setInterval(() => {}, 1000));
      }
      
      // Clean up all intervals
      intervals.forEach(interval => clearInterval(interval));
      
      expect(intervals).toHaveLength(5);
    });
  });
});
