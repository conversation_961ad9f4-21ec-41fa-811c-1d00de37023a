{"name": "ddjs_nextjs_app", "version": "0.1.0", "private": true, "scripts": {"dev": "cd apps/webapp && cp ../../.env.local .env.local 2>/dev/null || true && node server.js", "build": "npx nx build webapp", "start": "cd apps/webapp && cp ../../.env.local .env.local 2>/dev/null || true && NODE_ENV=production node server.js", "lint": "cd apps/webapp && next lint", "migrate:add-source-type": "tsx scripts/migrate-add-source-type.ts", "migrate:rollback-source-type": "tsx scripts/migrate-add-source-type.ts --rollback", "test:backend-implementation": "tsx scripts/test-backend-implementation.ts", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:headed": "playwright test --headed", "test:e2e:debug": "playwright test --debug", "test:install": "playwright install"}, "dependencies": {"@clerk/nextjs": "^6.12.6", "@google-cloud/functions-framework": "^3.0.0", "@google-cloud/pubsub": "^4.11.0", "@google-cloud/secret-manager": "^5.0.0", "@heroicons/react": "^2.2.0", "@playwright/test": "^1.51.1", "@radix-ui/react-checkbox": "^1.2.3", "@radix-ui/react-popover": "^1.1.11", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-select": "^2.1.6", "@tanstack/react-query": "^5.81.2", "@tanstack/react-query-devtools": "^5.81.2", "firebase": "^11.9.0", "firebase-admin": "^12.7.0", "firebase-functions": "^6.3.2", "geist": "^1.3.1", "googleapis": "^129.0.0", "js-base64": "^3.7.5", "next": "^14.0.4", "next-themes": "^0.4.5", "openai": "^4.103.0", "puppeteer": "^24.6.1", "react": "^18.3.1", "react-dom": "^18.3.1", "recharts": "^2.15.1", "stream-browserify": "^3.0.0", "zod": "^3.25.28"}, "devDependencies": {"@eslint/eslintrc": "^3", "@eslint/js": "~8.57.0", "@nx/esbuild": "21.1.2", "@nx/eslint-plugin": "21.1.2", "@nx/jest": "21.1.2", "@nx/js": "^21.1.2", "@nx/next": "^20.1.4", "@nx/node": "^21.1.2", "@swc-node/register": "~1.9.1", "@swc/core": "~1.5.7", "@swc/helpers": "~0.5.11", "@types/jest": "^29.5.12", "@types/node": "^20.17.24", "@types/react": "^18.3.18", "@types/react-datepicker": "^7.0.0", "@types/react-dom": "^18.3.5", "@types/uuid": "^10.0.0", "autoprefixer": "^10.0.1", "esbuild": "^0.25.4", "eslint": "^9", "eslint-config-next": "15.1.7", "jest": "^29.7.0", "jest-environment-node": "^29.7.0", "jsonc-eslint-parser": "^2.1.0", "nx": "21.1.2", "postcss": "^8.5.3", "prettier": "^2.6.2", "tailwindcss": "^3.3.0", "ts-jest": "^29.1.0", "ts-node": "^10.9.2", "tslib": "^2.3.0", "typescript": "^5.8.2", "typescript-eslint": "^8.32.1"}, "nx": {}, "workspaces": ["libs/*", "apps/*"]}