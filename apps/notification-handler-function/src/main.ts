// GCP Cloud Functions 2nd Gen imports with functions-framework
const functions = require('@google-cloud/functions-framework');
import { PubSub } from '@google-cloud/pubsub';
import { Database, Logger } from '@webapp/services';

// Initialize services
const logger = new Logger();
const database = new Database();
const pubsub = new PubSub();

// Gmail notification handler function (HTTP triggered)
functions.http('handleGmailNotification', async (req: any, res: any) => {
  try {
    // Verify this is a POST request
    if (req.method !== 'POST') {
      res.status(405).send('Method Not Allowed');
      return;
    }

    // Parse the Pub/Sub message from Gmail
    const message = req.body.message;
    if (!message || !message.data) {
      logger.warn('Invalid notification format received');
      res.status(400).send('Invalid notification format');
      return;
    }

    // Decode the message data
    const decodedData = Buffer.from(message.data, 'base64').toString();
    const notificationData = JSON.parse(decodedData);

    logger.info('Received Gmail notification', {
      historyId: notificationData.historyId,
      emailAddress: notificationData.emailAddress
    });

    // Get user ID from email address mapping
    const userMappingDoc = await database.collection('emailUserMapping')
      .doc(notificationData.emailAddress.replace(/[^a-zA-Z0-9@._-]/g, '_'))
      .get();

    if (!userMappingDoc.exists) {
      logger.warn('No user mapping found for email address', {
        emailAddress: notificationData.emailAddress
      });
      res.status(200).send('No user mapping found');
      return;
    }

    const { clerkUserId } = userMappingDoc.data() as { clerkUserId: string };

    // Get user preferences
    const preferencesDoc = await database.collection('notificationPreferences')
      .doc(clerkUserId)
      .get();

    const preferences = preferencesDoc.exists ? preferencesDoc.data() : {
      automaticAnalysis: false,
      skipInbox: {
        confirmations: false,
        rejections: false
      }
    };

    if (!preferences.automaticAnalysis) {
      logger.info('Automatic analysis disabled for user', { clerkUserId });
      res.status(200).send('Automatic analysis disabled');
      return;
    }

    // For now, publish a single analysis request with the history ID
    // TODO: Implement full Gmail history processing once deployment is stable
    const emailAnalysisMessage = {
      clerkUserId,
      messageId: `notification_${notificationData.historyId}`,
      monitoredEmail: notificationData.emailAddress,
      historyId: notificationData.historyId
    };

    await pubsub.topic('email-analysis-requests').publishMessage({
      data: Buffer.from(JSON.stringify(emailAnalysisMessage))
    });

    logger.info('Email analysis request published', {
      clerkUserId,
      historyId: notificationData.historyId
    });

    res.status(200).send('OK');

  } catch (error) {
    logger.error('Failed to process Gmail notification', {
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    });
    res.status(500).send('Internal Server Error');
  }
});
