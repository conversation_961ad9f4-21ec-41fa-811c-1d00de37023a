FROM node:20-alpine

WORKDIR /app

# Copy the entire standalone build (includes node_modules)
COPY .next/standalone ./
# Copy static files to the correct location for Next.js standalone
COPY .next/static ./apps/webapp/.next/static
COPY public ./apps/webapp/public

# Copy built shared library
COPY dist/libs/shared ./dist/libs/shared

# Expose port for Cloud Run
EXPOSE 8080

# Set PORT environment variable for Cloud Run
ENV PORT=8080

# Start the application using the standalone server
CMD ["node", "apps/webapp/server.js"]
