import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { useAuth } from '@clerk/nextjs'
import { queryKeys, cacheInvalidation } from '@/lib/react-query-provider'

/**
 * React Query hooks for analysis-related data fetching
 * Provides optimized caching and automatic refetching
 */

// Types
interface AnalysisRun {
  id: string
  status: 'idle' | 'processing' | 'completed' | 'error'
  sourceType: string
  createdAt: string
  updatedAt: string
  progress?: {
    processed: number
    total: number
    current: string
  }
  results?: any
  error?: string
}

interface AnalysisRunsResponse {
  runs: AnalysisRun[]
  total: number
  page: number
  totalPages: number
  hasMore: boolean
}

interface JobStatusResponse {
  jobs: Array<{
    id: string
    status: string
    progress: {
      processed: number
      total: number
      current: string
    }
    createdAt: string
    updatedAt: string
    error?: string
  }>
  meta: {
    requested: number
    found: number
    timestamp: string
  }
}

/**
 * Hook to fetch analysis runs with caching and pagination
 */
export function useAnalysisRuns(
  filters: {
    sourceType?: string
    status?: string
    page?: number
    limit?: number
  } = {}
) {
  const { getToken } = useAuth()

  return useQuery({
    queryKey: queryKeys.analysisRuns('current-user', filters),
    queryFn: async (): Promise<AnalysisRunsResponse> => {
      const token = await getToken()
      if (!token) throw new Error('Authentication required')

      const params = new URLSearchParams()
      if (filters.sourceType) params.append('sourceType', filters.sourceType)
      if (filters.status) params.append('status', filters.status)
      if (filters.page) params.append('page', filters.page.toString())
      if (filters.limit) params.append('limit', filters.limit.toString())

      const response = await fetch(`/api/analysis-runs?${params}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (!response.ok) {
        throw new Error(`Failed to fetch analysis runs: ${response.status}`)
      }

      return response.json()
    },
    staleTime: 30 * 1000, // 30 seconds for analysis runs (they change frequently)
    gcTime: 5 * 60 * 1000, // 5 minutes
    refetchInterval: (data) => {
      // Auto-refetch if there are active runs
      const hasActiveRuns = data?.runs?.some(run => run.status === 'processing')
      return hasActiveRuns ? 5000 : false // 5 seconds if active, no auto-refetch if idle
    },
    enabled: !!getToken, // Only run if authenticated
  })
}

/**
 * Hook to fetch a specific analysis run
 */
export function useAnalysisRun(runId: string) {
  const { getToken } = useAuth()

  return useQuery({
    queryKey: queryKeys.analysisRun(runId),
    queryFn: async (): Promise<AnalysisRun> => {
      const token = await getToken()
      if (!token) throw new Error('Authentication required')

      const response = await fetch(`/api/analysis-runs/${runId}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (!response.ok) {
        throw new Error(`Failed to fetch analysis run: ${response.status}`)
      }

      return response.json()
    },
    staleTime: 10 * 1000, // 10 seconds
    gcTime: 2 * 60 * 1000, // 2 minutes
    refetchInterval: (data) => {
      // Auto-refetch if run is processing
      return data?.status === 'processing' ? 3000 : false // 3 seconds if processing
    },
    enabled: !!runId && !!getToken,
  })
}

/**
 * Hook for batch job status queries (optimizes polling)
 */
export function useJobStatuses(jobIds: string[]) {
  const { getToken } = useAuth()

  return useQuery({
    queryKey: queryKeys.jobStatuses(jobIds),
    queryFn: async (): Promise<JobStatusResponse> => {
      const token = await getToken()
      if (!token) throw new Error('Authentication required')

      const response = await fetch('/api/jobs/batch-status', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ jobIds })
      })

      if (!response.ok) {
        throw new Error(`Failed to fetch job statuses: ${response.status}`)
      }

      return response.json()
    },
    staleTime: 2 * 1000, // 2 seconds (very fresh for job statuses)
    gcTime: 30 * 1000, // 30 seconds
    refetchInterval: (data) => {
      // Auto-refetch if any jobs are processing
      const hasProcessingJobs = data?.jobs?.some(job => job.status === 'processing')
      return hasProcessingJobs ? 3000 : false // 3 seconds if processing
    },
    enabled: jobIds.length > 0 && !!getToken,
  })
}

/**
 * Hook to fetch user metrics with caching
 */
export function useMetrics(dateRange?: { start: string; end: string }) {
  const { getToken } = useAuth()

  return useQuery({
    queryKey: queryKeys.metrics('current-user', dateRange),
    queryFn: async () => {
      const token = await getToken()
      if (!token) throw new Error('Authentication required')

      const params = new URLSearchParams()
      if (dateRange?.start) params.append('startDate', dateRange.start)
      if (dateRange?.end) params.append('endDate', dateRange.end)

      const response = await fetch(`/api/metrics?${params}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (!response.ok) {
        throw new Error(`Failed to fetch metrics: ${response.status}`)
      }

      return response.json()
    },
    staleTime: 2 * 60 * 1000, // 2 minutes (metrics don't change frequently)
    gcTime: 10 * 60 * 1000, // 10 minutes
    enabled: !!getToken,
  })
}

/**
 * Hook to fetch token information
 */
export function useTokens() {
  const { getToken } = useAuth()

  return useQuery({
    queryKey: queryKeys.tokens('current-user'),
    queryFn: async () => {
      const token = await getToken()
      if (!token) throw new Error('Authentication required')

      const response = await fetch('/api/tokens', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (!response.ok) {
        throw new Error(`Failed to fetch tokens: ${response.status}`)
      }

      return response.json()
    },
    staleTime: 30 * 1000, // 30 seconds (tokens can change during analysis)
    gcTime: 2 * 60 * 1000, // 2 minutes
    enabled: !!getToken,
  })
}

/**
 * Mutation hook for starting email analysis
 */
export function useStartAnalysis() {
  const { getToken } = useAuth()
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async ({ startDate, endDate }: { startDate: string; endDate: string }) => {
      const token = await getToken()
      if (!token) throw new Error('Authentication required')

      const response = await fetch('/api/emails/analyze', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ startDate, endDate })
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || 'Failed to start analysis')
      }

      return response.json()
    },
    onSuccess: () => {
      // Invalidate related caches when analysis starts
      cacheInvalidation.invalidateAnalysisRuns(queryClient, 'current-user')
      cacheInvalidation.invalidateTokens(queryClient, 'current-user')
    },
    onError: (error) => {
      console.error('Failed to start analysis:', error)
    }
  })
}

/**
 * Hook for email estimation with caching
 */
export function useEmailEstimate(startDate: string, endDate: string, enabled = true) {
  const { getToken } = useAuth()

  return useQuery({
    queryKey: queryKeys.emailEstimate('current-user', startDate, endDate),
    queryFn: async () => {
      const token = await getToken()
      if (!token) throw new Error('Authentication required')

      const response = await fetch('/api/emails/estimate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ startDate, endDate })
      })

      if (!response.ok) {
        throw new Error(`Failed to estimate emails: ${response.status}`)
      }

      return response.json()
    },
    staleTime: 5 * 60 * 1000, // 5 minutes (estimates are relatively stable)
    gcTime: 10 * 60 * 1000, // 10 minutes
    enabled: enabled && !!startDate && !!endDate && !!getToken,
  })
}
