import { auth } from '@clerk/nextjs/server';
import { verifyToken as clerkVerifyToken } from '@clerk/backend';

/**
 * Verify a JWT token from <PERSON> and return the userId if valid
 * @param token JWT token from Clerk
 * @returns userId if token is valid, null otherwise
 */
export async function verifyToken(token: string): Promise<string | null> {
  try {
    if (!token) {
      console.error('Token verification failed: No token provided');
      return null;
    }

    // SECURITY: Removed dangerous authentication bypass
    // All environments now use proper Clerk token verification
    // This ensures consistent security across development and production

    try {
      // Use Clerk's verifyToken function from @clerk/backend
      const verified = await clerkVerifyToken(token, {
        secretKey: process.env.CLERK_SECRET_KEY,
        // You can also use jwtKey for networkless verification if available
        // jwtKey: process.env.CLERK_JWT_KEY,
      });

      // The subject claim contains the user ID
      const userId = verified.sub;

      if (!userId) {
        console.error('Token verification failed: No subject in token');
        return null;
      }

      return userId;
    } catch (verifyError) {
      console.error('Error verifying token with Clerk:', verifyError);
      return null;
    }
  } catch (error) {
    console.error('Token verification error:', error);
    return null;
  }
}

/**
 * Get user information from Clerk by userId
 * @param userId Clerk user ID
 * @returns User object if found, null otherwise
 */
export async function getUserById(userId: string) {
  try {
    const { createClerkClient } = await import('@clerk/backend');

    if (!process.env.CLERK_SECRET_KEY) {
      console.error('CLERK_SECRET_KEY environment variable is required');
      return null;
    }

    const clerkClient = createClerkClient({
      secretKey: process.env.CLERK_SECRET_KEY
    });

    const user = await clerkClient.users.getUser(userId);

    if (!user) {
      console.error('User not found:', userId);
      return null;
    }

    // Extract primary email address
    const primaryEmail = user.emailAddresses.find(email => email.id === user.primaryEmailAddressId);

    return {
      id: user.id,
      email: primaryEmail?.emailAddress || '',
      firstName: user.firstName || '',
      lastName: user.lastName || '',
      imageUrl: user.imageUrl || '',
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
      lastSignInAt: user.lastSignInAt
    };
  } catch (error) {
    console.error('Error fetching user from Clerk:', error);
    return null;
  }
}
