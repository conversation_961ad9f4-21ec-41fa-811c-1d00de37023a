'use client'

import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { ReactQueryDevtools } from '@tanstack/react-query-devtools'
import { useState } from 'react'

/**
 * React Query configuration optimized for current usage scale
 * Provides client-side caching to reduce API calls and improve performance
 */
export function ReactQueryProvider({ children }: { children: React.ReactNode }) {
  const [queryClient] = useState(
    () =>
      new QueryClient({
        defaultOptions: {
          queries: {
            // Cache data for 5 minutes by default
            staleTime: 5 * 60 * 1000, // 5 minutes
            
            // Keep data in cache for 10 minutes
            gcTime: 10 * 60 * 1000, // 10 minutes (formerly cacheTime)
            
            // Retry failed requests 2 times
            retry: 2,
            
            // Retry with exponential backoff
            retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
            
            // Don't refetch on window focus for better UX
            refetchOnWindowFocus: false,
            
            // Refetch on reconnect
            refetchOnReconnect: true,
            
            // Don't refetch on mount if data is fresh
            refetchOnMount: 'always',
          },
          mutations: {
            // Retry mutations once
            retry: 1,
            
            // Retry delay for mutations
            retryDelay: 1000,
          },
        },
      })
  )

  return (
    <QueryClientProvider client={queryClient}>
      {children}
      {/* Only show devtools in development */}
      {process.env.NODE_ENV === 'development' && (
        <ReactQueryDevtools 
          initialIsOpen={false} 
          position="bottom-right"
        />
      )}
    </QueryClientProvider>
  )
}

/**
 * Query keys for consistent caching
 * Centralized query key management prevents cache misses
 */
export const queryKeys = {
  // Analysis runs
  analysisRuns: (userId: string, filters?: any) => 
    ['analysisRuns', userId, filters],
  
  // Individual analysis run
  analysisRun: (runId: string) => 
    ['analysisRun', runId],
  
  // Batch job statuses
  jobStatuses: (jobIds: string[]) => 
    ['jobStatuses', ...jobIds.sort()], // Sort for consistent caching
  
  // User metrics
  metrics: (userId: string, dateRange?: { start: string; end: string }) => 
    ['metrics', userId, dateRange],
  
  // Token information
  tokens: (userId: string) => 
    ['tokens', userId],
  
  // Email estimation
  emailEstimate: (userId: string, startDate: string, endDate: string) => 
    ['emailEstimate', userId, startDate, endDate],
  
  // User profile
  userProfile: (userId: string) => 
    ['userProfile', userId],
} as const

/**
 * Cache invalidation helpers
 * Provides easy ways to invalidate related caches
 */
export const cacheInvalidation = {
  // Invalidate all analysis runs for a user
  invalidateAnalysisRuns: (queryClient: QueryClient, userId: string) => {
    queryClient.invalidateQueries({
      queryKey: ['analysisRuns', userId]
    })
  },
  
  // Invalidate specific analysis run
  invalidateAnalysisRun: (queryClient: QueryClient, runId: string) => {
    queryClient.invalidateQueries({
      queryKey: ['analysisRun', runId]
    })
  },
  
  // Invalidate job statuses
  invalidateJobStatuses: (queryClient: QueryClient, jobIds: string[]) => {
    queryClient.invalidateQueries({
      queryKey: ['jobStatuses']
    })
  },
  
  // Invalidate user metrics
  invalidateMetrics: (queryClient: QueryClient, userId: string) => {
    queryClient.invalidateQueries({
      queryKey: ['metrics', userId]
    })
  },
  
  // Invalidate tokens
  invalidateTokens: (queryClient: QueryClient, userId: string) => {
    queryClient.invalidateQueries({
      queryKey: ['tokens', userId]
    })
  },
  
  // Invalidate all user data
  invalidateAllUserData: (queryClient: QueryClient, userId: string) => {
    queryClient.invalidateQueries({
      predicate: (query) => {
        const queryKey = query.queryKey
        return Array.isArray(queryKey) && queryKey.includes(userId)
      }
    })
  }
} as const

/**
 * Cache warming utilities
 * Pre-populate cache with likely-needed data
 */
export const cacheWarming = {
  // Warm analysis runs cache
  warmAnalysisRuns: async (queryClient: QueryClient, userId: string) => {
    await queryClient.prefetchQuery({
      queryKey: queryKeys.analysisRuns(userId),
      queryFn: () => fetch(`/api/analysis-runs?userId=${userId}`).then(res => res.json()),
      staleTime: 2 * 60 * 1000, // 2 minutes
    })
  },
  
  // Warm metrics cache
  warmMetrics: async (queryClient: QueryClient, userId: string) => {
    await queryClient.prefetchQuery({
      queryKey: queryKeys.metrics(userId),
      queryFn: () => fetch(`/api/metrics?userId=${userId}`).then(res => res.json()),
      staleTime: 5 * 60 * 1000, // 5 minutes
    })
  },
  
  // Warm tokens cache
  warmTokens: async (queryClient: QueryClient, userId: string) => {
    await queryClient.prefetchQuery({
      queryKey: queryKeys.tokens(userId),
      queryFn: () => fetch(`/api/tokens?userId=${userId}`).then(res => res.json()),
      staleTime: 1 * 60 * 1000, // 1 minute
    })
  }
} as const

/**
 * Performance monitoring for React Query
 */
export const performanceMonitoring = {
  // Log cache hit/miss rates
  logCacheStats: (queryClient: QueryClient) => {
    const cache = queryClient.getQueryCache()
    const queries = cache.getAll()
    
    const stats = {
      totalQueries: queries.length,
      freshQueries: queries.filter(q => q.state.dataUpdatedAt > Date.now() - 5 * 60 * 1000).length,
      staleQueries: queries.filter(q => q.isStale()).length,
      errorQueries: queries.filter(q => q.state.error).length,
    }
    
    console.log('React Query Cache Stats:', stats)
    return stats
  },
  
  // Monitor query performance
  setupPerformanceMonitoring: (queryClient: QueryClient) => {
    queryClient.getQueryCache().subscribe((event) => {
      if (event.type === 'updated' && event.action.type === 'success') {
        const duration = Date.now() - (event.query.state.dataUpdatedAt || 0)
        if (duration > 5000) { // Log slow queries (>5s)
          console.warn('Slow query detected:', {
            queryKey: event.query.queryKey,
            duration: `${duration}ms`
          })
        }
      }
    })
  }
} as const
