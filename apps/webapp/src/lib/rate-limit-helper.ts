import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { createRateLimitMiddleware, RATE_LIMITS } from '@webapp/shared';

/**
 * Apply rate limiting to an API route
 * @param req - Next.js request object
 * @param limitType - Type of rate limit to apply
 * @returns NextResponse with rate limit headers or null if allowed
 */
export async function applyRateLimit(
  req: NextRequest,
  limitType: keyof typeof RATE_LIMITS = 'general'
): Promise<NextResponse | null> {
  try {
    // Get user ID from Clerk authentication
    const { userId } = await auth();
    
    if (!userId) {
      // For unauthenticated requests, use IP address
      const ip = req.ip || req.headers.get('x-forwarded-for') || 'unknown';
      return applyRateLimitForKey(`ip:${ip}`, limitType);
    }

    return applyRateLimitForKey(`user:${userId}`, limitType);
  } catch (error) {
    console.error('Rate limiting error:', error);
    // On error, allow the request to proceed
    return null;
  }
}

/**
 * Apply rate limiting for a specific key
 */
function applyRateLimitForKey(
  key: string,
  limitType: keyof typeof RATE_LIMITS
): NextResponse | null {
  const config = RATE_LIMITS[limitType];
  const rateLimitMiddleware = createRateLimitMiddleware(config);
  const result = rateLimitMiddleware(key);

  if (!result.allowed) {
    // Rate limit exceeded
    return NextResponse.json(
      {
        error: 'Too Many Requests',
        message: 'Rate limit exceeded. Please try again later.',
        retryAfter: parseInt(result.headers['Retry-After'] || '60')
      },
      {
        status: 429,
        headers: result.headers
      }
    );
  }

  // Rate limit passed - return null to indicate the request should proceed
  // The headers will be added by the calling function
  return null;
}

/**
 * Add rate limit headers to a successful response
 */
export function addRateLimitHeaders(
  response: NextResponse,
  userId: string,
  limitType: keyof typeof RATE_LIMITS = 'general'
): NextResponse {
  try {
    const config = RATE_LIMITS[limitType];
    const rateLimitMiddleware = createRateLimitMiddleware(config);
    const result = rateLimitMiddleware(`user:${userId}`);

    // Add rate limit headers to the response
    Object.entries(result.headers).forEach(([key, value]) => {
      response.headers.set(key, value);
    });

    return response;
  } catch (error) {
    console.error('Error adding rate limit headers:', error);
    return response;
  }
}

/**
 * Wrapper function for API routes with rate limiting
 * @param handler - The actual API route handler
 * @param limitType - Type of rate limit to apply
 * @returns Wrapped handler with rate limiting
 */
export function withRateLimit<T extends any[]>(
  handler: (req: NextRequest, ...args: T) => Promise<NextResponse>,
  limitType: keyof typeof RATE_LIMITS = 'general'
) {
  return async (req: NextRequest, ...args: T): Promise<NextResponse> => {
    // Apply rate limiting
    const rateLimitResponse = await applyRateLimit(req, limitType);
    if (rateLimitResponse) {
      return rateLimitResponse;
    }

    // Rate limit passed, execute the handler
    const response = await handler(req, ...args);

    // Add rate limit headers to successful responses
    try {
      const { userId } = await auth();
      if (userId) {
        return addRateLimitHeaders(response, userId, limitType);
      }
    } catch (error) {
      console.error('Error adding rate limit headers:', error);
    }

    return response;
  };
}

/**
 * Simple rate limit check for use in API routes
 * @param userId - User ID to check
 * @param limitType - Type of rate limit
 * @returns true if allowed, false if rate limited
 */
export function checkRateLimit(
  userId: string,
  limitType: keyof typeof RATE_LIMITS = 'general'
): boolean {
  const config = RATE_LIMITS[limitType];
  const rateLimitMiddleware = createRateLimitMiddleware(config);
  const result = rateLimitMiddleware(`user:${userId}`);
  return result.allowed;
}
