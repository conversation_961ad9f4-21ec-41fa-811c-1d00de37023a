import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';

/**
 * Standard authentication result
 */
export interface AuthResult {
  userId: string | null;
  isAuthenticated: boolean;
}

/**
 * Standard authentication helper for API routes
 * Uses Clerk's auth() function consistently across all endpoints
 * 
 * @param req - NextRequest object (optional, for future extensibility)
 * @returns AuthResult with userId and authentication status
 */
export async function authenticateRequest(req?: NextRequest): Promise<AuthResult> {
  try {
    const { userId } = await auth();
    
    return {
      userId,
      isAuthenticated: !!userId
    };
  } catch (error) {
    console.error('Authentication failed:', error);
    return {
      userId: null,
      isAuthenticated: false
    };
  }
}

/**
 * Middleware wrapper that handles authentication for API routes
 * Returns 401 response if authentication fails, otherwise calls the handler
 * 
 * @param handler - The actual API route handler
 * @returns Wrapped handler with authentication
 */
export function withAuthentication<T extends any[]>(
  handler: (req: NextRequest, userId: string, ...args: T) => Promise<NextResponse>
) {
  return async (req: NextRequest, ...args: T): Promise<NextResponse> => {
    const authResult = await authenticateRequest(req);
    
    if (!authResult.isAuthenticated || !authResult.userId) {
      return NextResponse.json(
        { 
          error: 'Unauthorized',
          message: 'Authentication required. Please log in and try again.' 
        },
        { status: 401 }
      );
    }
    
    return handler(req, authResult.userId, ...args);
  };
}

/**
 * Create a standardized unauthorized response
 * 
 * @param message - Optional custom message
 * @returns NextResponse with 401 status
 */
export function createUnauthorizedResponse(message?: string): NextResponse {
  return NextResponse.json(
    {
      error: 'Unauthorized',
      message: message || 'Authentication required. Please log in and try again.',
      code: 'AUTH_REQUIRED'
    },
    { status: 401 }
  );
}

/**
 * Create a standardized forbidden response
 * 
 * @param message - Optional custom message
 * @returns NextResponse with 403 status
 */
export function createForbiddenResponse(message?: string): NextResponse {
  return NextResponse.json(
    {
      error: 'Forbidden',
      message: message || 'You do not have permission to access this resource.',
      code: 'INSUFFICIENT_PERMISSIONS'
    },
    { status: 403 }
  );
}

/**
 * Validate that a user ID belongs to the authenticated user
 * Prevents users from accessing other users' data
 * 
 * @param authenticatedUserId - The authenticated user's ID
 * @param requestedUserId - The user ID being requested
 * @returns true if valid, false otherwise
 */
export function validateUserAccess(
  authenticatedUserId: string,
  requestedUserId: string
): boolean {
  return authenticatedUserId === requestedUserId;
}

/**
 * Enhanced authentication helper that includes user access validation
 * 
 * @param req - NextRequest object
 * @param requiredUserId - Optional user ID that must match the authenticated user
 * @returns AuthResult with additional validation
 */
export async function authenticateAndValidateUser(
  req: NextRequest,
  requiredUserId?: string
): Promise<AuthResult & { hasValidAccess: boolean }> {
  const authResult = await authenticateRequest(req);
  
  if (!authResult.isAuthenticated || !authResult.userId) {
    return {
      ...authResult,
      hasValidAccess: false
    };
  }
  
  // If no specific user ID is required, access is valid
  if (!requiredUserId) {
    return {
      ...authResult,
      hasValidAccess: true
    };
  }
  
  // Validate that the authenticated user matches the required user ID
  const hasValidAccess = validateUserAccess(authResult.userId, requiredUserId);
  
  return {
    ...authResult,
    hasValidAccess
  };
}

/**
 * Wrapper for API routes that require specific user access
 * 
 * @param handler - The actual API route handler
 * @param getUserIdFromRequest - Function to extract user ID from request
 * @returns Wrapped handler with authentication and user validation
 */
export function withUserValidation<T extends any[]>(
  handler: (req: NextRequest, userId: string, ...args: T) => Promise<NextResponse>,
  getUserIdFromRequest?: (req: NextRequest, ...args: T) => string
) {
  return async (req: NextRequest, ...args: T): Promise<NextResponse> => {
    const requiredUserId = getUserIdFromRequest?.(req, ...args);
    const authResult = await authenticateAndValidateUser(req, requiredUserId);
    
    if (!authResult.isAuthenticated || !authResult.userId) {
      return createUnauthorizedResponse();
    }
    
    if (!authResult.hasValidAccess) {
      return createForbiddenResponse(
        'You can only access your own data.'
      );
    }
    
    return handler(req, authResult.userId, ...args);
  };
}

/**
 * Simple authentication check for use in API routes
 * Returns the user ID if authenticated, null otherwise
 * 
 * @returns Promise<string | null>
 */
export async function getCurrentUserId(): Promise<string | null> {
  try {
    const { userId } = await auth();
    return userId;
  } catch (error) {
    console.error('Error getting current user ID:', error);
    return null;
  }
}

/**
 * Check if the current request is authenticated
 * 
 * @returns Promise<boolean>
 */
export async function isAuthenticated(): Promise<boolean> {
  const userId = await getCurrentUserId();
  return !!userId;
}
