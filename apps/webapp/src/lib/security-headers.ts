import { NextResponse } from 'next/server';

/**
 * Security headers configuration
 */
export const SECURITY_HEADERS = {
  // Prevent clickjacking attacks
  'X-Frame-Options': 'DENY',
  
  // Prevent MIME type sniffing
  'X-Content-Type-Options': 'nosniff',
  
  // Control referrer information
  'Referrer-Policy': 'strict-origin-when-cross-origin',
  
  // Prevent XSS attacks
  'X-XSS-Protection': '1; mode=block',
  
  // Content Security Policy (restrictive but functional)
  'Content-Security-Policy': [
    "default-src 'self'",
    "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://clerk.com https://*.clerk.accounts.dev",
    "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
    "font-src 'self' https://fonts.gstatic.com",
    "img-src 'self' data: https:",
    "connect-src 'self' https://clerk.com https://*.clerk.accounts.dev https://*.googleapis.com",
    "frame-src 'self' https://clerk.com https://*.clerk.accounts.dev",
    "object-src 'none'",
    "base-uri 'self'",
    "form-action 'self'"
  ].join('; '),
  
  // Permissions Policy (formerly Feature Policy)
  'Permissions-Policy': [
    'camera=()',
    'microphone=()',
    'geolocation=()',
    'payment=()',
    'usb=()',
    'magnetometer=()',
    'accelerometer=()',
    'gyroscope=()'
  ].join(', '),
  
  // Strict Transport Security (HTTPS only)
  'Strict-Transport-Security': 'max-age=********; includeSubDomains; preload',
  
  // Cross-Origin policies
  'Cross-Origin-Embedder-Policy': 'require-corp',
  'Cross-Origin-Opener-Policy': 'same-origin',
  'Cross-Origin-Resource-Policy': 'same-origin'
} as const;

/**
 * Add security headers to a NextResponse
 * @param response - The response to add headers to
 * @returns The response with security headers added
 */
export function addSecurityHeaders(response: NextResponse): NextResponse {
  Object.entries(SECURITY_HEADERS).forEach(([key, value]) => {
    response.headers.set(key, value);
  });
  
  return response;
}

/**
 * Create a new response with security headers
 * @param data - Response data
 * @param init - Response init options
 * @returns NextResponse with security headers
 */
export function createSecureResponse(
  data: any,
  init?: ResponseInit
): NextResponse {
  const response = NextResponse.json(data, init);
  return addSecurityHeaders(response);
}

/**
 * Security headers for API routes (less restrictive CSP)
 */
export const API_SECURITY_HEADERS = {
  'X-Frame-Options': 'DENY',
  'X-Content-Type-Options': 'nosniff',
  'Referrer-Policy': 'strict-origin-when-cross-origin',
  'X-XSS-Protection': '1; mode=block',
  
  // More permissive CSP for API routes
  'Content-Security-Policy': "default-src 'none'; frame-ancestors 'none';",
  
  // HSTS
  'Strict-Transport-Security': 'max-age=********; includeSubDomains; preload',
  
  // Cross-Origin policies for APIs
  'Cross-Origin-Resource-Policy': 'cross-origin'
} as const;

/**
 * Add API-specific security headers
 * @param response - The response to add headers to
 * @returns The response with API security headers added
 */
export function addApiSecurityHeaders(response: NextResponse): NextResponse {
  Object.entries(API_SECURITY_HEADERS).forEach(([key, value]) => {
    response.headers.set(key, value);
  });
  
  return response;
}

/**
 * Create a secure API response
 * @param data - Response data
 * @param init - Response init options
 * @returns NextResponse with API security headers
 */
export function createSecureApiResponse(
  data: any,
  init?: ResponseInit
): NextResponse {
  const response = NextResponse.json(data, init);
  return addApiSecurityHeaders(response);
}

/**
 * Wrapper for API route handlers that automatically adds security headers
 * @param handler - The API route handler
 * @returns Wrapped handler with security headers
 */
export function withSecurityHeaders<T extends any[]>(
  handler: (req: Request, ...args: T) => Promise<NextResponse>
) {
  return async (req: Request, ...args: T): Promise<NextResponse> => {
    const response = await handler(req, ...args);
    return addApiSecurityHeaders(response);
  };
}

/**
 * CORS configuration for the application
 */
export const CORS_CONFIG = {
  development: {
    origins: [
      'http://localhost:3000',
      'https://dev.datadrivenjobsearch.com'
    ],
    credentials: true
  },
  production: {
    origins: [
      'https://datadrivenjobsearch.com'
    ],
    credentials: true
  }
} as const;

/**
 * Add CORS headers to a response
 * @param response - The response to add CORS headers to
 * @param origin - The request origin
 * @returns The response with CORS headers added
 */
export function addCorsHeaders(
  response: NextResponse,
  origin?: string
): NextResponse {
  const env = process.env.NODE_ENV as 'development' | 'production';
  const config = CORS_CONFIG[env] || CORS_CONFIG.development;
  
  // Check if origin is allowed
  if (origin && (config.origins as readonly string[]).includes(origin)) {
    response.headers.set('Access-Control-Allow-Origin', origin);
  } else if (env === 'development') {
    // Allow all origins in development
    response.headers.set('Access-Control-Allow-Origin', '*');
  }
  
  response.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With');
  
  if (config.credentials) {
    response.headers.set('Access-Control-Allow-Credentials', 'true');
  }
  
  return response;
}
