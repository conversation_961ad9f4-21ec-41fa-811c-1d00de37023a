import { clerkMiddleware } from '@clerk/nextjs/server'
import { NextResponse } from 'next/server'
import { addSecurityHeaders, addCorsHeaders } from '@/lib/security-headers'

// Enhanced middleware with security headers and CORS
export default clerkMiddleware((auth, req) => {
  // Get the response from Clerk middleware
  const response = NextResponse.next()

  // Add security headers to all responses
  addSecurityHeaders(response)

  // Add CORS headers for API routes
  if (req.nextUrl.pathname.startsWith('/api/')) {
    const origin = req.headers.get('origin')
    addCorsHeaders(response, origin || undefined)
  }

  return response
})

export const config = {
  matcher: [
    // Skip Next.js internals and static files
    '/((?!_next/|.*\\.(?:jpg|jpeg|gif|png|svg|ico|webp|js|css)).*)',
    // Force all API routes to go through middleware
    '/api/:path*',
  ],
} 