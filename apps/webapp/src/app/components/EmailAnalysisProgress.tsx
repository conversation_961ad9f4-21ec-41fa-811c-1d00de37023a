'use client'

import { useEffect, useState, forwardRef, useImperativeHandle } from 'react'
import { useAuth } from '@clerk/nextjs'
import { useTokens } from '../TokenContext'

// Define the progress state interface
interface ProgressState {
  total: number
  processed: number
  current: string
  status: 'idle' | 'processing' | 'completed' | 'error'
  cachedCount: number
  newAnalysisCount?: number
  remainingTokens?: number
  error?: string
  estimatedTotal?: number
  lastUpdated?: number
}

// Define the component ref interface
export interface EmailAnalysisProgressHandles {
  startAnalysis: (startDate: string, endDate: string) => Promise<void>
  isConnected: () => boolean
}

interface EmailAnalysisProgressProps {
  onAnalysisComplete?: (results: any) => void
  onAnalysisError?: (error: string) => void
}

export const EmailAnalysisProgress = forwardRef<
  EmailAnalysisProgressHandles,
  EmailAnalysisProgressProps
>(({ onAnalysisComplete, onAnalysisError }, ref) => {
  const { getToken } = useAuth()
  const { updateTokens } = useTokens()

  // Component state
  const [progress, setProgress] = useState<ProgressState>({
    total: 0,
    processed: 0,
    current: 'Ready to analyze emails',
    status: 'idle',
    cachedCount: 0
  })
  const [error, setError] = useState<string | null>(null)
  const [analysisInProgress, setAnalysisInProgress] = useState(false)
  const [currentJobId, setCurrentJobId] = useState<string | null>(null)
  const [pollingInterval, setPollingInterval] = useState<NodeJS.Timeout | null>(null)

  // Cleanup polling on unmount
  useEffect(() => {
    return () => {
      if (pollingInterval) {
        clearInterval(pollingInterval)
      }
    }
  }, [pollingInterval])

  // Function to poll job status
  const pollJobStatus = async (jobId: string) => {
    try {
      const token = await getToken()
      const response = await fetch(`/api/jobs/${jobId}/status`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (!response.ok) {
        throw new Error(`Failed to fetch job status: ${response.status}`)
      }

      const jobData = await response.json()
      
      // Update progress state
      setProgress({
        total: jobData.progress.total || 0,
        processed: jobData.progress.processed || 0,
        current: jobData.progress.current || 'Processing...',
        status: jobData.status,
        cachedCount: jobData.progress.cachedCount || 0,
        newAnalysisCount: jobData.progress.newAnalysisCount || 0,
        remainingTokens: jobData.progress.remainingTokens,
        error: jobData.progress.error,
        lastUpdated: Date.now()
      })

      // Update token count if available
      if (typeof jobData.progress.remainingTokens === 'number') {
        updateTokens(jobData.progress.remainingTokens)
      }

      // Check if job is complete
      if (jobData.status === 'completed') {
        setAnalysisInProgress(false)
        if (pollingInterval) {
          clearInterval(pollingInterval)
          setPollingInterval(null)
        }
        setCurrentJobId(null)
        
        if (onAnalysisComplete) {
          onAnalysisComplete(jobData.result)
        }
      } else if (jobData.status === 'error') {
        setAnalysisInProgress(false)
        if (pollingInterval) {
          clearInterval(pollingInterval)
          setPollingInterval(null)
        }
        setCurrentJobId(null)
        setError(jobData.progress.error || 'Analysis failed')
        
        if (onAnalysisError) {
          onAnalysisError(jobData.progress.error || 'Analysis failed')
        }
      }

    } catch (error) {
      console.error('Error polling job status:', error)
      setError(`Failed to check job status: ${error instanceof Error ? error.message : String(error)}`)
    }
  }

  // Start polling for a job
  const startPolling = (jobId: string) => {
    setCurrentJobId(jobId)
    
    // Poll immediately
    pollJobStatus(jobId)
    
    // Set up polling interval (every 2 seconds)
    const interval = setInterval(() => {
      pollJobStatus(jobId)
    }, 2000)
    
    setPollingInterval(interval)
  }

  // Expose the startAnalysis function via ref
  useImperativeHandle(ref, () => ({
    startAnalysis: async (startDate: string, endDate: string) => {
      try {
        setError(null)
        setAnalysisInProgress(true)
        setProgress(prev => ({
          ...prev,
          status: 'processing',
          current: 'Starting email analysis...',
          processed: 0,
          total: 0
        }))

        const token = await getToken()
        if (!token) {
          throw new Error('Authentication required')
        }

        // Start the analysis
        const response = await fetch('/api/emails/analyze', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          },
          body: JSON.stringify({ startDate, endDate })
        })

        const results = await response.json()

        // Update token information if available
        if (typeof results.remainingTokens === 'number') {
          updateTokens(results.remainingTokens)
        }

        if (!response.ok) {
          console.error('Analysis API call failed', results)
          setProgress(prev => ({
            ...prev,
            status: 'error',
            current: results.message || 'Analysis request failed'
          }))
          setAnalysisInProgress(false)
          throw new Error(results.message || 'Analysis failed')
        }

        // If we got a jobId, start polling
        if (results.jobId) {
          console.log('Starting polling for job:', results.jobId)
          startPolling(results.jobId)
        } else {
          // Fallback for backward compatibility
          setProgress(prev => ({
            ...prev,
            status: 'processing',
            current: 'Analysis started, waiting for updates...'
          }))
        }

      } catch (error) {
        setAnalysisInProgress(false)
        console.error('Error starting analysis:', error)
        setProgress(prev => ({
          ...prev,
          status: 'error',
          current: `Error: ${error instanceof Error ? error.message : String(error)}`
        }))
        throw error
      }
    },

    // For backward compatibility - polling is always "connected"
    isConnected: () => true
  }))

  // Render the component UI
  return (
    <div className="mb-8 p-6 bg-card rounded-lg shadow border">
      <h2 className="text-2xl font-bold mb-4 text-card-foreground">Email Analysis Progress</h2>

      {error && (
        <div className="mb-4 p-3 bg-red-100 text-red-800 rounded dark:bg-red-900 dark:text-red-200">
          {error}
        </div>
      )}

      <div className="space-y-4">
        {/* Progress Bar */}
        <div className="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700">
          <div
            className="bg-blue-600 h-2.5 rounded-full transition-all duration-300"
            style={{
              width: progress.total > 0 ? `${(progress.processed / progress.total) * 100}%` : '0%'
            }}
          ></div>
        </div>

        {/* Status Text */}
        <div className="text-sm text-gray-600 dark:text-gray-400">
          {progress.current}
        </div>

        {/* Progress Numbers */}
        {progress.total > 0 && (
          <div className="text-sm text-gray-500 dark:text-gray-500">
            {progress.processed} of {progress.total} emails processed
            {progress.cachedCount > 0 && ` (${progress.cachedCount} cached)`}
          </div>
        )}

        {/* Status Indicator */}
        <div className="flex items-center space-x-2">
          <div
            className={`w-3 h-3 rounded-full ${
              progress.status === 'processing'
                ? 'bg-blue-500 animate-pulse'
                : progress.status === 'completed'
                ? 'bg-green-500'
                : progress.status === 'error'
                ? 'bg-red-500'
                : 'bg-gray-400'
            }`}
          ></div>
          <span className="text-sm capitalize text-gray-600 dark:text-gray-400">
            {progress.status === 'processing' && analysisInProgress ? 'Processing' : progress.status}
          </span>
          {currentJobId && (
            <span className="text-xs text-gray-400">
              (Job: {currentJobId.substring(0, 8)}...)
            </span>
          )}
        </div>
      </div>
    </div>
  )
})

EmailAnalysisProgress.displayName = 'EmailAnalysisProgress'

export default EmailAnalysisProgress
