import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { BatchQueryService } from '@webapp/shared';
import { applyRateLimit } from '@/lib/rate-limit-helper';
import { createSecureApiResponse } from '@/lib/security-headers';

/**
 * Batch job status endpoint
 * Optimizes polling by allowing multiple job status queries in a single request
 * Replaces N+1 query pattern in real-time updates
 */
export async function POST(req: NextRequest) {
  try {
    // Apply rate limiting
    const rateLimitResponse = await applyRateLimit(req, 'general');
    if (rateLimitResponse) {
      return rateLimitResponse;
    }

    // Authentication
    const { userId } = await auth();
    if (!userId) {
      return createSecureApiResponse(
        { error: 'Unauthorized', message: 'Authentication required' },
        { status: 401 }
      );
    }

    // Parse request body
    const body = await req.json();
    const { jobIds } = body;

    // Validate input
    if (!Array.isArray(jobIds) || jobIds.length === 0) {
      return createSecureApiResponse(
        { 
          error: 'Invalid input', 
          message: 'jobIds must be a non-empty array' 
        },
        { status: 400 }
      );
    }

    // Limit batch size to prevent abuse
    if (jobIds.length > 50) {
      return createSecureApiResponse(
        { 
          error: 'Batch too large', 
          message: 'Maximum 50 jobs per batch request' 
        },
        { status: 400 }
      );
    }

    // Validate job IDs format
    const invalidJobIds = jobIds.filter(id => typeof id !== 'string' || id.length === 0);
    if (invalidJobIds.length > 0) {
      return createSecureApiResponse(
        { 
          error: 'Invalid job IDs', 
          message: 'All job IDs must be non-empty strings' 
        },
        { status: 400 }
      );
    }

    // Execute batch query
    const batchService = new BatchQueryService();
    const jobStatuses = await batchService.getMultipleJobStatuses(jobIds);

    // Filter jobs to only return those belonging to the authenticated user
    const userJobs = jobStatuses.filter(job => job.data?.clerkUserId === userId);

    // Transform response to match expected format
    const jobs = userJobs.map(job => ({
      id: job.id,
      status: job.data?.status || 'unknown',
      progress: {
        processed: job.data?.processed || 0,
        total: job.data?.total || 0,
        current: job.data?.current || ''
      },
      createdAt: job.data?.createdAt,
      updatedAt: job.data?.updatedAt,
      sourceType: job.data?.sourceType,
      error: job.data?.error,
      results: job.data?.results
    }));

    // Add performance metrics
    const responseData = {
      jobs,
      meta: {
        requested: jobIds.length,
        found: userJobs.length,
        filtered: jobStatuses.length - userJobs.length, // Jobs filtered out due to ownership
        timestamp: new Date().toISOString()
      }
    };

    return createSecureApiResponse(responseData);

  } catch (error) {
    console.error('Batch job status error:', error);
    
    return createSecureApiResponse(
      { 
        error: 'Internal server error', 
        message: 'Failed to fetch job statuses' 
      },
      { status: 500 }
    );
  }
}

/**
 * GET endpoint for batch job status with query parameters
 * Alternative to POST for simple use cases
 */
export async function GET(req: NextRequest) {
  try {
    // Apply rate limiting
    const rateLimitResponse = await applyRateLimit(req, 'general');
    if (rateLimitResponse) {
      return rateLimitResponse;
    }

    // Authentication
    const { userId } = await auth();
    if (!userId) {
      return createSecureApiResponse(
        { error: 'Unauthorized', message: 'Authentication required' },
        { status: 401 }
      );
    }

    // Parse query parameters
    const { searchParams } = new URL(req.url);
    const jobIdsParam = searchParams.get('jobIds');

    if (!jobIdsParam) {
      return createSecureApiResponse(
        { 
          error: 'Missing parameter', 
          message: 'jobIds query parameter is required' 
        },
        { status: 400 }
      );
    }

    // Parse comma-separated job IDs
    const jobIds = jobIdsParam.split(',').map(id => id.trim()).filter(id => id.length > 0);

    if (jobIds.length === 0) {
      return createSecureApiResponse(
        { 
          error: 'Invalid input', 
          message: 'At least one job ID is required' 
        },
        { status: 400 }
      );
    }

    // Limit batch size
    if (jobIds.length > 50) {
      return createSecureApiResponse(
        { 
          error: 'Batch too large', 
          message: 'Maximum 50 jobs per batch request' 
        },
        { status: 400 }
      );
    }

    // Execute batch query
    const batchService = new BatchQueryService();
    const jobStatuses = await batchService.getMultipleJobStatuses(jobIds);

    // Filter and transform response
    const userJobs = jobStatuses.filter(job => job.data?.clerkUserId === userId);
    const jobs = userJobs.map(job => ({
      id: job.id,
      status: job.data?.status || 'unknown',
      progress: {
        processed: job.data?.processed || 0,
        total: job.data?.total || 0,
        current: job.data?.current || ''
      },
      createdAt: job.data?.createdAt,
      updatedAt: job.data?.updatedAt,
      sourceType: job.data?.sourceType,
      error: job.data?.error
    }));

    const responseData = {
      jobs,
      meta: {
        requested: jobIds.length,
        found: userJobs.length,
        timestamp: new Date().toISOString()
      }
    };

    return createSecureApiResponse(responseData);

  } catch (error) {
    console.error('Batch job status GET error:', error);
    
    return createSecureApiResponse(
      { 
        error: 'Internal server error', 
        message: 'Failed to fetch job statuses' 
      },
      { status: 500 }
    );
  }
}
