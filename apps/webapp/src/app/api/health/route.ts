import { NextRequest, NextResponse } from 'next/server';

/**
 * Health check endpoint for deployment validation
 * Provides comprehensive system status for monitoring and deployment verification
 */

interface HealthCheckResult {
  status: 'healthy' | 'degraded' | 'unhealthy';
  timestamp: string;
  version: string;
  environment: string;
  checks: {
    database: HealthCheck;
    authentication: HealthCheck;
    environment: HealthCheck;
    dependencies: HealthCheck;
  };
  performance: {
    responseTime: number;
    memoryUsage: NodeJS.MemoryUsage;
    uptime: number;
  };
  deployment: {
    buildTime?: string;
    commitHash?: string;
    deploymentId?: string;
  };
}

interface HealthCheck {
  status: 'pass' | 'fail' | 'warn';
  message: string;
  details?: any;
  responseTime?: number;
}

export async function GET(req: NextRequest): Promise<NextResponse> {
  const startTime = Date.now();

  try {
    const healthResult = await performHealthChecks();
    const responseTime = Date.now() - startTime;

    // Add performance metrics
    healthResult.performance.responseTime = responseTime;

    // Determine overall status
    const overallStatus = determineOverallStatus(healthResult.checks);
    healthResult.status = overallStatus;

    // Return appropriate HTTP status code
    const httpStatus = overallStatus === 'healthy' ? 200 :
                      overallStatus === 'degraded' ? 200 : 503;

    return NextResponse.json(healthResult, { status: httpStatus });

  } catch (error) {
    console.error('Health check failed:', error);

    const errorResult: HealthCheckResult = {
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      version: process.env.npm_package_version || 'unknown',
      environment: process.env.NODE_ENV || 'unknown',
      checks: {
        database: { status: 'fail', message: 'Health check failed' },
        authentication: { status: 'fail', message: 'Health check failed' },
        environment: { status: 'fail', message: 'Health check failed' },
        dependencies: { status: 'fail', message: 'Health check failed' }
      },
      performance: {
        responseTime: Date.now() - startTime,
        memoryUsage: process.memoryUsage(),
        uptime: process.uptime()
      },
      deployment: {}
    };

    return NextResponse.json(errorResult, { status: 503 });
  }
}

/**
 * Perform all health checks
 */
async function performHealthChecks(): Promise<HealthCheckResult> {
  const checks = await Promise.allSettled([
    checkDatabase(),
    checkAuthentication(),
    checkEnvironment(),
    checkDependencies()
  ]);
  
  return {
    status: 'healthy', // Will be determined later
    timestamp: new Date().toISOString(),
    version: process.env.npm_package_version || 'unknown',
    environment: process.env.NODE_ENV || 'unknown',
    checks: {
      database: checks[0].status === 'fulfilled' ? checks[0].value :
        { status: 'fail', message: 'Database check failed', details: checks[0].reason },
      authentication: checks[1].status === 'fulfilled' ? checks[1].value :
        { status: 'fail', message: 'Authentication check failed', details: checks[1].reason },
      environment: checks[2].status === 'fulfilled' ? checks[2].value :
        { status: 'fail', message: 'Environment check failed', details: checks[2].reason },
      dependencies: checks[3].status === 'fulfilled' ? checks[3].value :
        { status: 'fail', message: 'Dependencies check failed', details: checks[3].reason }
    },
    performance: {
      responseTime: 0, // Will be set later
      memoryUsage: process.memoryUsage(),
      uptime: process.uptime()
    },
    deployment: {
      buildTime: process.env.BUILD_TIME,
      commitHash: process.env.COMMIT_HASH,
      deploymentId: process.env.DEPLOYMENT_ID
    }
  };
}

/**
 * Check database connectivity
 */
async function checkDatabase(): Promise<HealthCheck> {
  const startTime = Date.now();

  try {
    // For now, just check if we have the project ID configured
    const projectId = process.env.GOOGLE_CLOUD_PROJECT || process.env.GCLOUD_PROJECT;

    if (!projectId) {
      return {
        status: 'warn',
        message: 'Database configuration incomplete - no project ID',
        responseTime: Date.now() - startTime,
        details: 'GOOGLE_CLOUD_PROJECT environment variable not set'
      };
    }

    const responseTime = Date.now() - startTime;

    return {
      status: 'pass',
      message: 'Database configuration valid',
      responseTime,
      details: {
        projectId: projectId
      }
    };
  } catch (error) {
    return {
      status: 'fail',
      message: 'Database check failed',
      responseTime: Date.now() - startTime,
      details: error instanceof Error ? error.message : String(error)
    };
  }
}

/**
 * Check authentication configuration
 */
async function checkAuthentication(): Promise<HealthCheck> {
  try {
    const clerkSecretKey = process.env.CLERK_SECRET_KEY;
    const clerkPublishableKey = process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY;

    const issues: string[] = [];

    if (!clerkSecretKey) {
      issues.push('Clerk secret key not configured');
    }

    if (!clerkPublishableKey) {
      issues.push('Clerk publishable key not configured');
    }

    if (issues.length > 0) {
      return {
        status: 'fail',
        message: 'Authentication configuration incomplete',
        details: issues
      };
    }

    return {
      status: 'pass',
      message: 'Authentication configuration valid',
      details: {
        hasSecretKey: !!clerkSecretKey,
        hasPublishableKey: !!clerkPublishableKey
      }
    };
  } catch (error) {
    return {
      status: 'fail',
      message: 'Authentication check failed',
      details: error instanceof Error ? error.message : String(error)
    };
  }
}

/**
 * Check environment configuration
 */
async function checkEnvironment(): Promise<HealthCheck> {
  try {
    const nodeEnv = process.env.NODE_ENV || 'development';
    const port = process.env.PORT || '3000';
    const projectId = process.env.GOOGLE_CLOUD_PROJECT || process.env.GCLOUD_PROJECT;
    const openaiApiKey = process.env.OPENAI_API_KEY;

    const warnings: string[] = [];

    // Check for development-specific warnings in production
    if (nodeEnv === 'production') {
      if (!openaiApiKey) {
        warnings.push('OpenAI API key not configured');
      }

      if (!process.env.GOOGLE_APPLICATION_CREDENTIALS && !projectId) {
        warnings.push('GCP service account credentials not configured');
      }
    }

    const status = warnings.length > 0 ? 'warn' : 'pass';

    return {
      status,
      message: status === 'pass' ? 'Environment configuration valid' : 'Environment has warnings',
      details: {
        nodeEnv,
        port,
        projectId,
        warnings: warnings.length > 0 ? warnings : undefined
      }
    };
  } catch (error) {
    return {
      status: 'fail',
      message: 'Environment check failed',
      details: error instanceof Error ? error.message : String(error)
    };
  }
}

/**
 * Check critical dependencies
 */
async function checkDependencies(): Promise<HealthCheck> {
  try {
    const dependencies = {
      node: process.version,
      npm: process.env.npm_version,
      platform: process.platform,
      arch: process.arch
    };
    
    const warnings: string[] = [];
    
    // Check Node.js version
    const nodeVersion = parseInt(process.version.slice(1).split('.')[0]);
    if (nodeVersion < 18) {
      warnings.push(`Node.js version ${process.version} is below recommended 18+`);
    }
    
    const status = warnings.length > 0 ? 'warn' : 'pass';
    
    return {
      status,
      message: status === 'pass' ? 'Dependencies check passed' : 'Dependencies have warnings',
      details: {
        ...dependencies,
        warnings: warnings.length > 0 ? warnings : undefined
      }
    };
  } catch (error) {
    return {
      status: 'fail',
      message: 'Dependencies check failed',
      details: error instanceof Error ? error.message : String(error)
    };
  }
}

/**
 * Determine overall health status
 */
function determineOverallStatus(checks: HealthCheckResult['checks']): 'healthy' | 'degraded' | 'unhealthy' {
  const checkValues = Object.values(checks);
  
  // If any check fails, system is unhealthy
  if (checkValues.some(check => check.status === 'fail')) {
    return 'unhealthy';
  }
  
  // If any check has warnings, system is degraded
  if (checkValues.some(check => check.status === 'warn')) {
    return 'degraded';
  }
  
  // All checks pass
  return 'healthy';
}
