'use client';

import { useEffect, useRef, useCallback } from 'react';

export interface UseRealTimeUpdatesProps {
  activeRunIds: string[];
  onUpdate: (jobId: string) => void;
  interval?: number;
  enabled?: boolean;
  adaptivePolling?: boolean;
  maxInterval?: number;
  minInterval?: number;
}

export function useRealTimeUpdates({
  activeRunIds,
  onUpdate,
  interval = 5000, // 5 seconds default
  enabled = true,
  adaptivePolling = true,
  maxInterval = 15000, // 15 seconds max (optimized for current scale)
  minInterval = 3000   // 3 seconds min (optimized for current scale)
}: UseRealTimeUpdatesProps) {
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const isPollingRef = useRef(false);
  const currentIntervalRef = useRef(interval);
  const consecutiveErrorsRef = useRef(0);
  const lastUpdateTimeRef = useRef<Record<string, number>>({});

  const adjustPollingInterval = useCallback(() => {
    if (!adaptivePolling) return;

    const now = Date.now();
    const recentActivity = activeRunIds.some(jobId => {
      const lastUpdate = lastUpdateTimeRef.current[jobId];
      return lastUpdate && (now - lastUpdate) < 60000; // Activity in last minute
    });

    if (consecutiveErrorsRef.current > 0) {
      // Slow down on errors
      currentIntervalRef.current = Math.min(
        currentIntervalRef.current * 1.5,
        maxInterval
      );
    } else if (recentActivity) {
      // Speed up for active jobs
      currentIntervalRef.current = Math.max(
        currentIntervalRef.current * 0.8,
        minInterval
      );
    } else {
      // Gradually slow down for inactive jobs
      currentIntervalRef.current = Math.min(
        currentIntervalRef.current * 1.2,
        maxInterval
      );
    }
  }, [adaptivePolling, activeRunIds, maxInterval, minInterval]);

  const pollActiveRuns = useCallback(async () => {
    if (!enabled || activeRunIds.length === 0 || isPollingRef.current) {
      return;
    }

    isPollingRef.current = true;
    const startTime = Date.now();

    try {
      // Poll each active run for updates
      const updatePromises = activeRunIds.map(async (jobId) => {
        try {
          await onUpdate(jobId);
          lastUpdateTimeRef.current[jobId] = Date.now();
          return { jobId, success: true };
        } catch (error) {
          console.error(`Failed to update job ${jobId}:`, error);
          return { jobId, success: false, error };
        }
      });

      const results = await Promise.all(updatePromises);

      // Track errors for adaptive polling
      const errors = results.filter(r => !r.success);
      if (errors.length > 0) {
        consecutiveErrorsRef.current++;
      } else {
        consecutiveErrorsRef.current = 0;
      }

      // Adjust polling interval based on performance
      adjustPollingInterval();

    } catch (error) {
      console.error('Failed to poll active runs:', error);
      consecutiveErrorsRef.current++;
    } finally {
      isPollingRef.current = false;

      // Log performance metrics in development
      if (process.env.NODE_ENV === 'development') {
        const duration = Date.now() - startTime;
        console.log(`Polling completed in ${duration}ms, next interval: ${currentIntervalRef.current}ms`);
      }
    }
  }, [activeRunIds, onUpdate, enabled, adjustPollingInterval]);

  // Set up adaptive polling interval
  useEffect(() => {
    if (!enabled || activeRunIds.length === 0) {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
      // Reset interval when no active runs
      currentIntervalRef.current = interval;
      consecutiveErrorsRef.current = 0;
      return;
    }

    // Clear existing interval
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }

    // Start polling with current interval
    const startPolling = () => {
      intervalRef.current = setInterval(pollActiveRuns, currentIntervalRef.current);
    };

    startPolling();

    // Restart polling when interval changes (adaptive polling)
    const intervalCheckTimer = setInterval(() => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        startPolling();
      }
    }, Math.max(currentIntervalRef.current, 5000)); // Check every 5 seconds minimum

    // Cleanup on unmount or dependency change
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
      if (intervalCheckTimer) {
        clearInterval(intervalCheckTimer);
      }
    };
  }, [activeRunIds, enabled, pollActiveRuns]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);

  // Manual trigger for immediate update
  const triggerUpdate = useCallback(() => {
    pollActiveRuns();
  }, [pollActiveRuns]);

  return {
    triggerUpdate,
    isPolling: isPollingRef.current
  };
}
