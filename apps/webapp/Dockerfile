FROM node:20-alpine

WORKDIR /app

# Copy the entire standalone build (includes node_modules)
COPY .next/standalone ./
# Copy static files to the correct location for Next.js standalone
COPY .next/static ./apps/webapp/.next/static
COPY public ./apps/webapp/public

# Note: Shared library is bundled in the standalone build

# Set environment variables
ENV NODE_ENV=production
ENV PORT=8080

# Expose port for Cloud Run
EXPOSE 8080

# Start the application using the standalone server
CMD ["node", "apps/webapp/server.js"]
