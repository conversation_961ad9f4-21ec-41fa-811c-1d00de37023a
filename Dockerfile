# Simple deployment using existing build
FROM node:20-alpine

WORKDIR /app

# Copy the standalone build (already built locally)
COPY .next/standalone ./
COPY .next/static ./apps/webapp/.next/static
COPY public ./apps/webapp/public

# Note: Shared library is bundled in the standalone build

# Set environment variables
ENV NODE_ENV=production
ENV PORT=8080

# Expose the port
EXPOSE 8080

# Start the application using the standalone server
CMD ["node", "apps/webapp/server.js"]