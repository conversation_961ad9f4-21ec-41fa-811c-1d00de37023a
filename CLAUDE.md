# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is "Data Driven Job Search" - a Next.js web application that helps users analyze their job search emails using Gmail integration and AI analysis. The application uses Clerk for authentication, GCP for hosting, and Firebase/Firestore for data storage.

## Architecture

### Monorepo Structure (Nx)
- **apps/webapp**: Next.js web application (main frontend)
- **apps/email-analysis-function**: GCP Cloud Function for email analysis
- **apps/notification-handler-function**: GCP Cloud Function for Gmail webhook notifications
- **libs/shared**: Common utilities and shared code
- **libs/services**: Business logic services (auth, database, email processing)
- **libs/email-core**: Core email analysis logic with OpenAI integration
- **libs/gcp-utils**: Google Cloud Platform utilities

### Key Technologies
- **Frontend**: Next.js 14, React 18, Tailwind CSS, Radix UI components
- **Authentication**: <PERSON> (@clerk/nextjs)
- **Backend**: Google Cloud Functions, Firebase/Firestore
- **Email Processing**: Gmail API, OpenAI API for email classification
- **Build System**: Nx monorepo with TypeScript
- **Testing**: Playwright for E2E, Jest for unit tests

## Development Commands

### Local Development
```bash
# Quick development start (recommended)
./scripts/start-dev.sh

# Manual development start
npm run dev

# Run with Docker
./RunLocally.sh --docker

# Clear database and run fresh
./RunLocally.sh --clear-data
```

### Building and Testing
```bash
# Build all projects
npx nx run-many --target=build --all

# Build specific project
npx nx build webapp
npx nx build email-analysis-function

# Run tests
npm run test:e2e              # Playwright E2E tests
npm run test:e2e:ui           # Playwright with UI
npm run test:e2e:debug        # Playwright debug mode
npx nx test shared            # Jest unit tests for specific lib

# Linting
npm run lint                  # Lint webapp
npx nx lint email-core        # Lint specific project
```

### Deployment
```bash
# Deploy to GCP development environment
./scripts/deploy-to-gcp.sh

# Deploy only webapp
./scripts/deploy-webapp-only.sh

# Deploy only functions
./scripts/deploy-functions.sh

# Complete deployment with testing
./scripts/deploy-complete.sh
```

### Self-Update CLAUDE.md
```bash
# When you learn new patterns, commands, or architectural insights, update this file:
# 1. Edit CLAUDE.md with new learnings
# 2. Commit changes with detailed message about what was learned
# Example commit message: "docs: Update CLAUDE.md with new email processing patterns learned during debugging"
```

## AUGMENT DEVELOPMENT GUIDELINES

### Critical Rules
- **NEVER create mock functions or endpoints** - assume everything will go to production
- **No sensitive information** in git commits (API keys, personal information)
- **Regular commits** with informative messages outlining accomplishments, trade-offs, and next steps
- **Production-ready code only** - all implementations must be fully functional

### Development Workflow
1. **Review existing docs** in the `docs/` folder
2. **Create `docs_proposed/`** folder documenting the new desired system state
3. **Create step-by-step plan** breaking work into smallest testable tasks
4. **Test after each task** to ensure everything works as expected
5. **Update `docs/` folder** as work completes, moving everything from `docs_proposed/`

### Before Work Completion
1. **Run all automated regression tests**
2. **Deploy to dev environment**: `./scripts/deploy-complete.sh`
3. **Test application** at dev.datadrivenjobsearch.com using Playwright tools

### Required Playwright Tests
1. Test email analysis from Jan 1, 2023 to Jan 3, 2023 - analysis must complete successfully
2. Validate user tokens are not 0
3. Check for console errors using Playwright console tool
4. Check cloud server logs using gcloud tools for errors

### Error Handling Protocol
1. **No errors expected** in dev environment (uses real API endpoints)
2. Use Playwright console for debugging information
3. Use gcloud tools for cloud server log analysis
4. **Brainstorm 3+ potential causes** for any issue
5. **Create additional logging** to isolate root cause
6. **Fix and repeat** until no errors remain

## Important Architecture Notes

### Email Processing Flow
1. User authenticates via Clerk and connects Gmail
2. Gmail push notifications trigger `notification-handler-function`
3. `email-analysis-function` processes emails using OpenAI for classification
4. Results stored in Firestore with email linking logic for job application tracking
5. Real-time updates via polling mechanism in frontend

### Authentication Architecture
- Clerk handles user authentication and session management
- Gmail OAuth integration for email access
- Token refresh handled automatically
- Session management supports multiple devices per user

### Database Schema
- Firestore collections: users, analysis-runs, email-analysis-results
- Email linking logic connects related emails for same job application
- Caching to avoid reprocessing same emails

### Environment Configuration
- Development: Uses Firestore emulators via `./scripts/test-locally.sh`
- Production: GCP project `data-driven-job-search`
- Development deployment: GCP project `ddjs-dev-458016`
- Secrets managed via Google Secret Manager

## Key Configuration Files

### Nx Configuration
- `nx.json`: Monorepo build configuration and project dependencies
- `project.json` in each app/lib: Project-specific build targets
- `tsconfig.base.json`: Shared TypeScript configuration

### Development Rules (from .cursor/rules)
- Always check package.json for current package versions
- Reference Requirements.md for project requirements
- Ask clarifying questions when requirements are ambiguous
- Outline steps before proceeding and get confirmation
- Track progress through each step
- Alert when changes might remove functionality

## Firebase Emulator Usage

The project uses Firebase emulators for local development:
```bash
# Start emulators only
./scripts/start-emulators-only.sh

# Test locally with emulators
./scripts/test-locally.sh
```

## Common Development Patterns

### Adding New API Routes
- Place in `apps/webapp/src/app/api/`
- Follow existing patterns for error handling and authentication
- Use services from `libs/services` for business logic

### Working with Email Analysis
- Core logic in `libs/email-core`
- OpenAI integration for email classification
- Email content truncated at 5000 characters
- Supports text/plain and text/html MIME types

### Real-time Updates
- Uses polling mechanism (not WebSockets)
- Progress tracking for email analysis jobs
- Metrics update dynamically during processing

## Security Notes

- API keys and credentials stored in Google Secret Manager
- HTTPS enforced in production
- Secure session management with unique session IDs per device
- Rate limiting implemented for API endpoints

## Testing Strategy

- E2E tests with Playwright covering critical user workflows
- Unit tests with Jest for shared libraries
- Production validation scripts in `scripts/` directory
- Comprehensive test coverage for email analysis pipeline

## Documentation Standards

- Use Mermaid diagrams validated with mermaid-validator tool
- Maintain both `docs/` (current state) and `docs_proposed/` (planned changes)
- Reference `Requirements.md` for project requirements
- Update this CLAUDE.md file when learning new patterns or commands