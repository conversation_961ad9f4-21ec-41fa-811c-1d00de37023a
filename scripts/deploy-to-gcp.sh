#!/bin/bash

# 🚀 Deploy to GCP Development Environment
# This script deploys the entire application to Firebase/GCP
# Domain: dev.datadrivenjobsearch.com
# Project: ddjs-dev-458016

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_ID="ddjs-dev-458016"  # Deployment target (dev environment)
SECRETS_PROJECT_ID="data-driven-job-search"  # Where secrets are stored (prod project)
DOMAIN="dev.datadrivenjobsearch.com"
REGION="us-central1"

echo -e "${BLUE}🚀 Starting GCP Deployment for Data Driven Job Search${NC}"
echo -e "${BLUE}Deployment Target: ${PROJECT_ID}${NC}"
echo -e "${BLUE}Secrets Source: ${SECRETS_PROJECT_ID}${NC}"
echo -e "${BLUE}Domain: ${DOMAIN}${NC}"
echo ""

# Function to print status
print_status() {
    echo -e "${YELLOW}$1${NC}"
}

# Function to print success
print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

# Function to print error
print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check prerequisites
print_status "🔍 Checking prerequisites..."

# Skip Firebase CLI check since we're using pure GCP

# Check if gcloud CLI is installed
if ! command -v gcloud &> /dev/null; then
    print_error "Google Cloud CLI is not installed. Please install it first:"
    echo "https://cloud.google.com/sdk/docs/install"
    exit 1
fi

# Skip Firebase login check since we're using pure GCP

# Check if user is logged in to gcloud
if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | head -n1 &> /dev/null; then
    print_error "Not logged in to Google Cloud. Please run:"
    echo "gcloud auth login"
    exit 1
fi

print_success "Prerequisites check passed"

# Set the active project
print_status "🎯 Setting active project..."
gcloud config set project $PROJECT_ID
print_success "Project set to $PROJECT_ID"

# Build all projects except webapp (which has static generation issues)
print_status "🔨 Building Cloud Functions and core libraries..."
npx nx reset || true
npx nx run-many --target=build --projects=shared,email-core,email-analysis-function,notification-handler-function

if [ $? -ne 0 ]; then
    print_error "Build failed"
    exit 1
fi
print_success "Cloud Functions and core libraries built successfully"

# Try to build webapp separately with error handling
print_status "🌐 Attempting to build webapp..."
if npx nx run webapp:build; then
    print_success "Webapp built successfully"
    WEBAPP_BUILD_SUCCESS=true
else
    print_error "Webapp build failed - will deploy Cloud Functions only"
    print_status "You can fix the webapp build issue and redeploy later"
    WEBAPP_BUILD_SUCCESS=false
fi

# Prepare Cloud Functions
print_status "📦 Preparing Cloud Functions..."

# Create proper package.json for each function (without workspace dependencies)
cat > dist/apps/email-analysis-function/package.json << EOF
{
  "name": "email-analysis-function",
  "version": "1.0.0",
  "private": true,
  "main": "main.js",
  "engines": {
    "node": "20"
  },
  "dependencies": {
    "@clerk/backend": "^1.33.0",
    "@google-cloud/functions-framework": "^3.0.0",
    "@google-cloud/secret-manager": "^5.0.0",
    "@google-cloud/pubsub": "^4.0.0",
    "firebase-admin": "^12.0.0",
    "openai": "^4.0.0",
    "googleapis": "^129.0.0",
    "js-base64": "^3.7.7",
    "zod": "^3.22.0",
    "tslib": "^2.3.0"
  }
}
EOF

cat > dist/apps/notification-handler-function/package.json << EOF
{
  "name": "notification-handler-function",
  "version": "1.0.0",
  "private": true,
  "main": "main.js",
  "scripts": {
    "start": "node main.js"
  },
  "engines": {
    "node": "20"
  },
  "dependencies": {
    "@clerk/backend": "^1.33.0",
    "@google-cloud/functions-framework": "^3.0.0",
    "@google-cloud/secret-manager": "^5.0.0",
    "@google-cloud/pubsub": "^4.0.0",
    "firebase-admin": "^12.0.0",
    "googleapis": "^129.0.0",
    "js-base64": "^3.7.7",
    "openai": "^4.0.0",
    "zod": "^3.22.0",
    "tslib": "^2.3.0"
  }
}
EOF

# Ensure main.js files exist (they should be built by nx)
if [ ! -f "dist/apps/email-analysis-function/main.js" ]; then
    print_error "email-analysis-function main.js not found after build"
    exit 1
fi

if [ ! -f "dist/apps/notification-handler-function/main.js" ]; then
    print_error "notification-handler-function main.js not found after build"
    exit 1
fi

print_success "Cloud Functions prepared"

# Enable required APIs
print_status "🔧 Enabling required Google Cloud APIs..."
gcloud services enable cloudfunctions.googleapis.com
gcloud services enable firestore.googleapis.com
gcloud services enable pubsub.googleapis.com
gcloud services enable run.googleapis.com
gcloud services enable cloudbuild.googleapis.com
gcloud services enable gmail.googleapis.com
gcloud services enable secretmanager.googleapis.com
print_success "APIs enabled"

# Create Pub/Sub topics (check if they exist first)
print_status "📨 Creating Pub/Sub topics..."

# Check and create email-analysis-requests topic
if gcloud pubsub topics describe email-analysis-requests --project=$PROJECT_ID >/dev/null 2>&1; then
    print_success "✅ Topic 'email-analysis-requests' already exists"
else
    print_status "Creating topic 'email-analysis-requests'..."
    gcloud pubsub topics create email-analysis-requests --project=$PROJECT_ID
    print_success "✅ Topic 'email-analysis-requests' created"
fi

# Check and create gmail-notifications topic
if gcloud pubsub topics describe gmail-notifications --project=$PROJECT_ID >/dev/null 2>&1; then
    print_success "✅ Topic 'gmail-notifications' already exists"
else
    print_status "Creating topic 'gmail-notifications'..."
    gcloud pubsub topics create gmail-notifications --project=$PROJECT_ID
    print_success "✅ Topic 'gmail-notifications' created"
fi

print_success "Pub/Sub topics ready"

# Deploy GCP Cloud Functions with Secret Manager integration
print_status "☁️ Deploying GCP Cloud Functions..."

# Prepare function source directories
print_status "Preparing Cloud Functions source code..."
mkdir -p dist/apps/email-analysis-function/src
mkdir -p dist/apps/notification-handler-function/src

# Copy built dependencies to function directories
print_status "Copying built dependencies..."
cp -r dist/libs/* dist/apps/email-analysis-function/ 2>/dev/null || true
cp -r dist/libs/* dist/apps/notification-handler-function/ 2>/dev/null || true

# Copy source files
cp -r apps/email-analysis-function/src/* dist/apps/email-analysis-function/src/ 2>/dev/null || true
cp -r apps/notification-handler-function/src/* dist/apps/notification-handler-function/src/ 2>/dev/null || true

# Deploy email analysis function (Pub/Sub triggered)
print_status "Deploying email analysis function..."
if gcloud functions deploy analyzeEmail \
    --runtime=nodejs20 \
    --trigger-topic=email-analysis-requests \
    --entry-point=analyzeEmail \
    --source=dist/apps/email-analysis-function \
    --set-env-vars=SECRETS_PROJECT_ID=$SECRETS_PROJECT_ID,WEBAPP_PROGRESS_WEBHOOK_URL=https://$DOMAIN/api/emails/progress-webhook \
    --region=$REGION \
    --project=$PROJECT_ID \
    --gen2; then
    print_success "Email analysis function deployed successfully"
    EMAIL_FUNCTION_SUCCESS=true
else
    print_error "Email analysis function deployment failed"
    EMAIL_FUNCTION_SUCCESS=false
fi

# Deploy notification handler as Cloud Function (HTTP triggered)
print_status "Deploying notification handler function..."
if gcloud functions deploy handleGmailNotification \
    --runtime=nodejs20 \
    --trigger-http \
    --allow-unauthenticated \
    --entry-point=handleGmailNotification \
    --source=dist/apps/notification-handler-function \
    --set-env-vars=SECRETS_PROJECT_ID=$SECRETS_PROJECT_ID \
    --region=$REGION \
    --project=$PROJECT_ID \
    --gen2; then
    print_success "Notification handler function deployed successfully"
    NOTIFICATION_FUNCTION_SUCCESS=true
else
    print_error "Notification handler function deployment failed (continuing with webapp deployment)"
    NOTIFICATION_FUNCTION_SUCCESS=false
fi

# Grant Secret Manager access to Cloud Functions service account
print_status "🔐 Configuring Secret Manager permissions for Cloud Functions..."
FUNCTIONS_SERVICE_ACCOUNT="$<EMAIL>"

print_status "Granting Secret Manager access to Cloud Functions service account: $FUNCTIONS_SERVICE_ACCOUNT"
print_status "Granting access to secrets in production project: $SECRETS_PROJECT_ID"

# Grant access to secrets in the production project with condition
print_status "Adding IAM policy binding with condition for Cloud Functions..."
gcloud projects add-iam-policy-binding $SECRETS_PROJECT_ID \
    --member="serviceAccount:$FUNCTIONS_SERVICE_ACCOUNT" \
    --role="roles/secretmanager.secretAccessor" \
    --condition="expression=true,title=Cloud Functions Secret Access,description=Allow Cloud Functions from dev project to access secrets" || {
    print_status "IAM binding with condition failed, trying without condition..."
    gcloud projects add-iam-policy-binding $SECRETS_PROJECT_ID \
        --member="serviceAccount:$FUNCTIONS_SERVICE_ACCOUNT" \
        --role="roles/secretmanager.secretAccessor" || {
        print_error "Failed to grant Secret Manager access to Cloud Functions"
        print_status "You may need to manually grant access in the Google Cloud Console"
    }
}

print_success "GCP Cloud Functions deployed with Secret Manager integration"

# Retrieve secrets from Google Cloud Secret Manager
print_status "🔐 Retrieving secrets from Google Cloud Secret Manager..."
print_status "Secrets will be retrieved from project: $SECRETS_PROJECT_ID"

# Function to safely retrieve secrets
retrieve_secret() {
    local secret_name=$1
    local var_name=$2

    print_status "Fetching $secret_name from $SECRETS_PROJECT_ID..."
    local secret_value=$(gcloud secrets versions access latest --secret="$secret_name" --project=$SECRETS_PROJECT_ID 2>/dev/null)

    if [ -z "$secret_value" ]; then
        print_error "Failed to retrieve secret: $secret_name from project $SECRETS_PROJECT_ID"
        echo "Please ensure:"
        echo "1. The secret exists in Google Secret Manager in project $SECRETS_PROJECT_ID"
        echo "2. You have access permissions to the $SECRETS_PROJECT_ID project"
        echo "3. You have the secretmanager.secretAccessor role"
        echo ""
        echo "To check if the secret exists:"
        echo "gcloud secrets list --project=$SECRETS_PROJECT_ID --filter=\"name:$secret_name\""
        return 1
    fi

    export $var_name="$secret_value"
    print_success "$secret_name retrieved successfully from $SECRETS_PROJECT_ID"
    return 0
}

# Retrieve all required secrets - Continue even if some fail
print_status "Retrieving required secrets for webapp deployment..."
retrieve_secret "CLERK_PUBLISHABLE_KEY" "NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY"
CLERK_PUB_SUCCESS=$?
retrieve_secret "CLERK_SECRET_KEY" "CLERK_SECRET_KEY"
CLERK_SECRET_SUCCESS=$?
retrieve_secret "OPENAI_API_KEY" "OPENAI_API_KEY"
OPENAI_SUCCESS=$?

# Check if we have the minimum required secrets for webapp
if [ $CLERK_PUB_SUCCESS -eq 0 ] && [ $CLERK_SECRET_SUCCESS -eq 0 ]; then
    print_success "✅ Essential secrets retrieved - webapp deployment can proceed"
    SECRETS_SUCCESS=true
else
    print_error "❌ Failed to retrieve essential secrets - webapp may not work properly"
    SECRETS_SUCCESS=false
fi

# Deploy webapp only if build was successful
if [ "$WEBAPP_BUILD_SUCCESS" = true ]; then
    # Build webapp for production
    print_status "🌐 Building webapp for production..."
    cd apps/webapp

    # Create production environment file dynamically from secrets
    print_status "Creating production environment file from secrets..."
cat > .env.production << EOF
# Production environment variables - Generated from Google Secret Manager
NODE_ENV=production
GOOGLE_CLOUD_PROJECT=$PROJECT_ID

# Base URL for production
BASE_URL=https://$DOMAIN

# Clerk Authentication - Retrieved from Secret Manager
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=$NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY
CLERK_SECRET_KEY=$CLERK_SECRET_KEY
NEXT_PUBLIC_CLERK_SIGN_IN_URL=/sign-in
NEXT_PUBLIC_CLERK_SIGN_UP_URL=/sign-up
NEXT_PUBLIC_CLERK_AFTER_SIGN_IN_URL=/
NEXT_PUBLIC_CLERK_AFTER_SIGN_UP_URL=/

# OpenAI API Key - Retrieved from Secret Manager
OPENAI_API_KEY=$OPENAI_API_KEY

# Webhook URL for Cloud Functions
WEBAPP_PROGRESS_WEBHOOK_URL=https://$DOMAIN/api/emails/progress-webhook

# Gmail API Configuration
GMAIL_PUBSUB_TOPIC=projects/$PROJECT_ID/topics/gmail-notifications
GMAIL_PUBSUB_SUBSCRIPTION=gmail-notifications-sub

# Cloud Functions Configuration
FUNCTIONS_REGION=$REGION
EOF

print_success "Production environment file created from Secret Manager"

# Build the Next.js app
npm run build

if [ $? -ne 0 ]; then
    print_error "Webapp build failed"
    exit 1
fi

# Copy built libraries to webapp directory for Docker context
print_status "📦 Copying built libraries to webapp directory..."
mkdir -p dist/libs
# Note: shared library is bundled in Next.js standalone build, no need to copy separately

cd ../..
print_success "Webapp built for production"

# Deploy to Cloud Run
print_status "🏃 Deploying webapp to Cloud Run..."

# Create production Dockerfile (use Next.js standalone build)
cat > apps/webapp/Dockerfile.production << EOF
FROM node:20-alpine

WORKDIR /app

# Copy the entire standalone build (includes node_modules)
COPY .next/standalone ./
# Copy static files to the correct location for Next.js standalone
COPY .next/static ./apps/webapp/.next/static
COPY public ./apps/webapp/public

# Note: Shared library is bundled in the standalone build

# Set environment variables
ENV NODE_ENV=production
ENV PORT=8080

# Expose port for Cloud Run
EXPOSE 8080

# Start the application using the standalone server
CMD ["node", "apps/webapp/server.js"]
EOF



# Enable Secret Manager API
print_status "🔧 Enabling Secret Manager API..."
gcloud services enable secretmanager.googleapis.com --project=$PROJECT_ID

# Build and deploy to Cloud Run
print_status "🏗️ Building container image..."
cd apps/webapp
# Copy the production Dockerfile to the default name for gcloud builds submit
cp Dockerfile.production Dockerfile
gcloud builds submit . \
  --tag gcr.io/$PROJECT_ID/webapp \
  --project=$PROJECT_ID
# Clean up the temporary Dockerfile
rm Dockerfile
cd ../..

print_status "🚀 Deploying to Cloud Run with environment variables..."

# Prepare environment variables for Cloud Run deployment
ENV_VARS="GOOGLE_CLOUD_PROJECT=$PROJECT_ID,SECRETS_PROJECT_ID=$SECRETS_PROJECT_ID,NODE_ENV=production"

# Add Clerk environment variables if available
if [ $CLERK_PUB_SUCCESS -eq 0 ]; then
    ENV_VARS="$ENV_VARS,NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=$NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY"
fi
if [ $CLERK_SECRET_SUCCESS -eq 0 ]; then
    ENV_VARS="$ENV_VARS,CLERK_SECRET_KEY=$CLERK_SECRET_KEY"
fi
if [ $OPENAI_SUCCESS -eq 0 ]; then
    ENV_VARS="$ENV_VARS,OPENAI_API_KEY=$OPENAI_API_KEY"
fi

# Add additional environment variables
ENV_VARS="$ENV_VARS,NEXT_PUBLIC_CLERK_SIGN_IN_URL=/sign-in"
ENV_VARS="$ENV_VARS,NEXT_PUBLIC_CLERK_SIGN_UP_URL=/sign-up"
ENV_VARS="$ENV_VARS,NEXT_PUBLIC_CLERK_AFTER_SIGN_IN_URL=/"
ENV_VARS="$ENV_VARS,NEXT_PUBLIC_CLERK_AFTER_SIGN_UP_URL=/"

print_status "Environment variables prepared for deployment"

gcloud run deploy webapp \
  --image gcr.io/$PROJECT_ID/webapp \
  --platform managed \
  --region $REGION \
  --allow-unauthenticated \
  --port 8080 \
  --set-env-vars "$ENV_VARS" \
  --project $PROJECT_ID

# Grant Secret Manager access to Cloud Run service account
print_status "🔐 Configuring Secret Manager permissions..."
SERVICE_ACCOUNT=$(gcloud run services describe webapp --region=$REGION --format="value(spec.template.spec.serviceAccountName)" --project=$PROJECT_ID)

if [ -z "$SERVICE_ACCOUNT" ]; then
    # Use default compute service account if no custom service account is set
    SERVICE_ACCOUNT="$(gcloud projects describe $PROJECT_ID --format="value(projectNumber)")-<EMAIL>"
fi

print_status "Granting Secret Manager access to service account: $SERVICE_ACCOUNT"
print_status "Granting access to secrets in production project: $SECRETS_PROJECT_ID"

# Grant access to secrets in the production project with condition
print_status "Adding IAM policy binding with condition for Cloud Run..."
gcloud projects add-iam-policy-binding $SECRETS_PROJECT_ID \
    --member="serviceAccount:$SERVICE_ACCOUNT" \
    --role="roles/secretmanager.secretAccessor" \
    --condition="expression=true,title=Cloud Run Secret Access,description=Allow Cloud Run from dev project to access secrets" || {
    print_status "IAM binding with condition failed, trying without condition..."
    gcloud projects add-iam-policy-binding $SECRETS_PROJECT_ID \
        --member="serviceAccount:$SERVICE_ACCOUNT" \
        --role="roles/secretmanager.secretAccessor" || {
        print_error "Failed to grant Secret Manager access to Cloud Run"
        print_status "You may need to manually grant access in the Google Cloud Console"
    }
}

print_status "Secret Manager permissions configured for cross-project access"

if [ $? -ne 0 ]; then
    print_error "Cloud Run deployment failed"
    exit 1
fi

print_success "Webapp deployed to Cloud Run"

# Get the Cloud Run URL
CLOUD_RUN_URL=$(gcloud run services describe webapp --region=$REGION --format="value(status.url)" --project=$PROJECT_ID)
print_success "Cloud Run URL: $CLOUD_RUN_URL"

else
    print_status "⚠️ Skipping webapp deployment due to build failure"
    print_status "Cloud Functions have been deployed successfully"
    print_status "Fix the webapp build issue and run the deployment again"
    CLOUD_RUN_URL="(webapp deployment skipped)"
fi

echo ""
echo -e "${BLUE}📋 DEPLOYMENT SUMMARY${NC}"
echo "===================="

# Report Cloud Functions status
echo -e "${BLUE}☁️ Cloud Functions:${NC}"
if [ "$EMAIL_FUNCTION_SUCCESS" = true ]; then
    echo "✅ Email Analysis Function: Successfully deployed"
else
    echo "❌ Email Analysis Function: Deployment failed"
fi

if [ "$NOTIFICATION_FUNCTION_SUCCESS" = true ]; then
    echo "✅ Notification Handler Function: Successfully deployed"
else
    echo "❌ Notification Handler Function: Deployment failed"
fi

# Report Webapp status
echo -e "${BLUE}🌐 Webapp:${NC}"
if [ "$WEBAPP_BUILD_SUCCESS" = true ]; then
    echo "✅ Webapp: Successfully deployed to Cloud Run"
    echo "   URL: $CLOUD_RUN_URL"
    if [ "$SECRETS_SUCCESS" = true ]; then
        echo "✅ Environment Variables: Properly configured"
    else
        echo "⚠️ Environment Variables: Some secrets missing"
    fi
else
    echo "❌ Webapp: Build failed"
fi

# Report Infrastructure status
echo -e "${BLUE}🏗️ Infrastructure:${NC}"
echo "✅ Pub/Sub Topics: Created/Verified"
echo "✅ Secret Manager: Cross-project access configured"
echo "✅ APIs: All required APIs enabled"

echo ""
if [ "$WEBAPP_BUILD_SUCCESS" = true ]; then
    echo -e "${GREEN}🎉 Deployment completed successfully!${NC}"
    echo ""
    echo -e "${BLUE}📋 Next Steps:${NC}"
    echo "1. Test the webapp: $CLOUD_RUN_URL"
    echo "2. Set up domain mapping for: https://$DOMAIN"
    echo "3. Configure SSL certificate"
    echo "4. Update DNS records"
    echo ""
    echo -e "${BLUE}🌐 URLs:${NC}"
    echo "Cloud Run URL: $CLOUD_RUN_URL"
    echo "Target Domain: https://$DOMAIN"
    echo "Google Cloud Console: https://console.cloud.google.com/run?project=$PROJECT_ID"
else
    echo -e "${YELLOW}⚠️ Partial deployment completed!${NC}"
    echo ""
    echo -e "${BLUE}📋 Next Steps:${NC}"
    echo "1. Fix the webapp build issue and re-run deployment"
    echo "2. Test Cloud Functions functionality"
    echo "3. Set up domain mapping after webapp is deployed"
fi
