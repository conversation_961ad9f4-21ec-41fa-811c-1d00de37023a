#!/bin/bash

# 🌐 Deploy Webapp Only to GCP
# This script deploys only the webapp to Cloud Run with proper environment variables

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_ID="ddjs-dev-458016"  # Deployment target (dev environment)
SECRETS_PROJECT_ID="data-driven-job-search"  # Where secrets are stored (prod project)
DOMAIN="dev.datadrivenjobsearch.com"
REGION="us-central1"

echo -e "${BLUE}🌐 Starting Webapp-Only Deployment${NC}"
echo -e "${BLUE}Deployment Target: ${PROJECT_ID}${NC}"
echo -e "${BLUE}Secrets Source: ${SECRETS_PROJECT_ID}${NC}"
echo ""

# Function to print status
print_status() {
    echo -e "${YELLOW}$1${NC}"
}

# Function to print success
print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

# Function to print error
print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check prerequisites
print_status "🔍 Checking prerequisites..."

if ! command -v gcloud &> /dev/null; then
    print_error "Google Cloud CLI is not installed"
    exit 1
fi

if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | head -n1 &> /dev/null; then
    print_error "Not logged in to Google Cloud. Please run: gcloud auth login"
    exit 1
fi

print_success "Prerequisites check passed"

# Set the active project
print_status "🎯 Setting active project..."
gcloud config set project $PROJECT_ID
print_success "Project set to $PROJECT_ID"

# Build webapp
print_status "🔨 Building webapp..."
cd apps/webapp
npm run build

if [ $? -ne 0 ]; then
    print_error "Webapp build failed"
    exit 1
fi

print_success "Webapp built successfully"

# Function to safely retrieve secrets
retrieve_secret() {
    local secret_name=$1
    local var_name=$2

    print_status "Fetching $secret_name from $SECRETS_PROJECT_ID..."
    local secret_value=$(gcloud secrets versions access latest --secret="$secret_name" --project=$SECRETS_PROJECT_ID 2>/dev/null)

    if [ $? -eq 0 ] && [ ! -z "$secret_value" ]; then
        export $var_name="$secret_value"
        print_success "✅ $secret_name retrieved successfully"
        return 0
    else
        print_error "❌ Failed to retrieve $secret_name"
        return 1
    fi
}

# Retrieve secrets
print_status "🔐 Retrieving secrets from Google Cloud Secret Manager..."
retrieve_secret "CLERK_PUBLISHABLE_KEY" "NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY"
CLERK_PUB_SUCCESS=$?
retrieve_secret "CLERK_SECRET_KEY" "CLERK_SECRET_KEY"
CLERK_SECRET_SUCCESS=$?
retrieve_secret "OPENAI_API_KEY" "OPENAI_API_KEY"
OPENAI_SUCCESS=$?

# Check if we have essential secrets
if [ $CLERK_PUB_SUCCESS -eq 0 ] && [ $CLERK_SECRET_SUCCESS -eq 0 ]; then
    print_success "✅ Essential secrets retrieved"
    SECRETS_SUCCESS=true
else
    print_error "❌ Failed to retrieve essential secrets"
    SECRETS_SUCCESS=false
fi

# Deploy to Cloud Run with environment variables
print_status "🚀 Deploying webapp to Cloud Run..."

# Prepare environment variables
ENV_VARS="GOOGLE_CLOUD_PROJECT=$PROJECT_ID,SECRETS_PROJECT_ID=$SECRETS_PROJECT_ID,NODE_ENV=production"

# Add Clerk environment variables if available
if [ $CLERK_PUB_SUCCESS -eq 0 ]; then
    ENV_VARS="$ENV_VARS,NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=$NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY"
fi
if [ $CLERK_SECRET_SUCCESS -eq 0 ]; then
    ENV_VARS="$ENV_VARS,CLERK_SECRET_KEY=$CLERK_SECRET_KEY"
fi
if [ $OPENAI_SUCCESS -eq 0 ]; then
    ENV_VARS="$ENV_VARS,OPENAI_API_KEY=$OPENAI_API_KEY"
fi

# Add additional environment variables
ENV_VARS="$ENV_VARS,NEXT_PUBLIC_CLERK_SIGN_IN_URL=/sign-in"
ENV_VARS="$ENV_VARS,NEXT_PUBLIC_CLERK_SIGN_UP_URL=/sign-up"
ENV_VARS="$ENV_VARS,NEXT_PUBLIC_CLERK_AFTER_SIGN_IN_URL=/"
ENV_VARS="$ENV_VARS,NEXT_PUBLIC_CLERK_AFTER_SIGN_UP_URL=/"

print_status "Environment variables prepared for deployment"

# Deploy using the existing Dockerfile
gcloud run deploy webapp \
  --source=. \
  --platform=managed \
  --region=$REGION \
  --allow-unauthenticated \
  --port=8080 \
  --memory=1Gi \
  --cpu=1 \
  --set-env-vars="$ENV_VARS" \
  --project=$PROJECT_ID

if [ $? -ne 0 ]; then
    print_error "Cloud Run deployment failed"
    exit 1
fi

# Get the Cloud Run URL
CLOUD_RUN_URL=$(gcloud run services describe webapp --region=$REGION --format="value(status.url)" --project=$PROJECT_ID)

print_success "Webapp deployed successfully!"

echo ""
echo -e "${GREEN}🎉 Webapp deployment completed!${NC}"
echo ""
echo -e "${BLUE}🌐 URLs:${NC}"
echo "Cloud Run URL: $CLOUD_RUN_URL"
echo "Target Domain: https://$DOMAIN"
echo ""
if [ "$SECRETS_SUCCESS" = true ]; then
    echo -e "${GREEN}✅ Environment Variables: Properly configured${NC}"
    echo "The webapp should be fully functional with authentication"
else
    echo -e "${YELLOW}⚠️ Environment Variables: Some secrets missing${NC}"
    echo "The webapp may have authentication issues"
fi
echo ""
echo -e "${BLUE}📋 Next Steps:${NC}"
echo "1. Test the webapp: $CLOUD_RUN_URL"
echo "2. Set up domain mapping for: https://$DOMAIN"

cd ../..
