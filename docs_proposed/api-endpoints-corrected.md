# 🔌 API Endpoints - Corrected Documentation

## 🚨 Critical Corrections

### Removed Endpoints (No Longer Exist)
- ❌ `/api/socket` - Socket.IO endpoint (completely removed)
- ❌ `/socket.io/` - Socket.IO server endpoint (never implemented)

### Actual API Structure

```
/api/
├── analysis-runs/
│   └── route.ts                   # ✅ Analysis job management
├── emails/
│   ├── analyze/route.ts           # ✅ Trigger email analysis
│   ├── estimate/route.ts          # ✅ Estimate analysis cost
│   ├── progress/route.ts          # ✅ SSE progress stream
│   └── progress-webhook/route.ts  # ✅ Cloud Function webhook
├── jobs/
│   └── [jobId]/
│       └── route.ts               # ✅ Individual job status
├── metrics/
│   └── route.ts                   # ✅ Analytics data
├── tokens/
│   └── route.ts                   # ✅ Token management
└── socket/                        # ❌ EMPTY DIRECTORY
```

## 📋 Verified API Endpoints

### 1. Analysis Management

#### POST /api/emails/analyze
**Purpose**: Trigger email analysis for a date range

**Authentication**: Required (Clerk JWT)

**Request Body**:
```typescript
interface AnalyzeEmailsRequest {
  startDate: string;  // ISO date string
  endDate: string;    // ISO date string
}
```

**Response**:
```typescript
interface AnalyzeEmailsResponse {
  success: boolean;
  message: string;
  jobId?: string;
  estimatedTokens?: number;
  totalEmails?: number;
}
```

**Implementation Details**:
- Validates date range
- Checks user token balance
- Creates Pub/Sub messages for Cloud Functions
- Returns immediately with job ID

#### GET /api/emails/estimate
**Purpose**: Estimate token cost for email analysis

**Authentication**: Required (Clerk JWT)

**Query Parameters**:
- `startDate`: ISO date string
- `endDate`: ISO date string

**Response**:
```typescript
interface EstimateResponse {
  totalEmails: number;
  estimatedTokens: number;
  userTokens: number;
  sufficient: boolean;
}
```

### 2. Real-time Progress (SSE)

#### GET /api/emails/progress
**Purpose**: Server-Sent Events stream for real-time progress updates

**Authentication**: Required (Clerk JWT via query parameter or header)

**Runtime**: Edge (for better streaming performance)

**Response Format**: `text/event-stream`

**Event Data Structure**:
```typescript
interface ProgressUpdate {
  status: 'idle' | 'processing' | 'completed' | 'error' | 'connecting';
  total?: number;
  processed?: number;
  current?: string;
  cachedCount?: number;
  estimatedTokens?: number | null;
  remainingTokens?: number | null;
  tokenError?: string | null;
}
```

**Connection Features**:
- Automatic ping/keepalive (30-second intervals)
- Graceful connection cleanup
- In-memory connection store
- Error handling and reconnection support

### 3. Analysis Jobs Management

#### GET /api/analysis-runs
**Purpose**: Retrieve user's analysis job history

**Authentication**: Required (Clerk JWT)

**Query Parameters**:
- `limit?`: number (default: 50)
- `offset?`: number (default: 0)
- `status?`: 'processing' | 'completed' | 'error'

**Response**:
```typescript
interface AnalysisRun {
  id: string;
  status: 'processing' | 'completed' | 'error';
  startDate: string;
  endDate: string;
  totalEmails: number;
  processedEmails: number;
  createdAt: string;
  completedAt?: string;
  errorMessage?: string;
}

interface AnalysisRunsResponse {
  runs: AnalysisRun[];
  total: number;
  hasMore: boolean;
}
```

#### GET /api/jobs/[jobId]
**Purpose**: Get specific job status and details

**Authentication**: Required (Clerk JWT)

**Response**:
```typescript
interface JobStatusResponse {
  job: AnalysisRun;
  progress?: {
    current: string;
    processed: number;
    total: number;
  };
}
```

### 4. Metrics & Analytics

#### GET /api/metrics
**Purpose**: Get user's email analysis metrics

**Authentication**: Required (Clerk JWT)

**Query Parameters**:
- `startDate?`: ISO date string
- `endDate?`: ISO date string
- `groupBy?`: 'day' | 'week' | 'month'

**Response**:
```typescript
interface MetricsResponse {
  applications: {
    total: number;
    byStatus: Record<string, number>;
    timeline: Array<{
      date: string;
      count: number;
    }>;
  };
  interviews: {
    total: number;
    byType: Record<string, number>;
    timeline: Array<{
      date: string;
      count: number;
    }>;
  };
  responses: {
    total: number;
    responseRate: number;
    timeline: Array<{
      date: string;
      count: number;
    }>;
  };
}
```

### 5. Token Management

#### GET /api/tokens
**Purpose**: Get user's token balance and usage

**Authentication**: Required (Clerk JWT)

**Response**:
```typescript
interface TokenResponse {
  remaining: number;
  total: number;
  used: number;
  lastUpdated: string;
  subscription?: {
    plan: string;
    renewalDate: string;
    monthlyAllowance: number;
  };
}
```

### 6. Webhook Endpoints

#### POST /api/emails/progress-webhook
**Purpose**: Receive progress updates from Cloud Functions

**Authentication**: Internal (Cloud Function to webapp communication)

**Request Body**:
```typescript
interface ProgressWebhookPayload {
  userId: string;
  jobId: string;
  status: 'processing' | 'completed' | 'error';
  progress?: {
    current: string;
    processed: number;
    total: number;
  };
  error?: string;
}
```

**Response**: `200 OK` or error status

## 🔒 Authentication Patterns

### JWT Token Validation
All endpoints use Clerk JWT tokens for authentication:

```typescript
import { auth } from '@clerk/nextjs/server'

export async function GET(req: NextRequest) {
  const { userId } = await auth()
  
  if (!userId) {
    return NextResponse.json({ message: 'Unauthorized' }, { status: 401 })
  }
  
  // Proceed with authenticated request
}
```

### SSE Authentication
SSE endpoints accept tokens via query parameter for EventSource compatibility:

```typescript
// Client-side
const token = await getToken()
const eventSource = new EventSource(`/api/emails/progress?token=${token}`)
```

## ⚠️ Error Handling

### Standard Error Response Format
```typescript
interface ErrorResponse {
  error: string;
  message: string;
  code?: string;
  details?: any;
}
```

### Common HTTP Status Codes
- `200`: Success
- `400`: Bad Request (invalid parameters)
- `401`: Unauthorized (missing/invalid JWT)
- `403`: Forbidden (insufficient permissions)
- `429`: Too Many Requests (rate limiting)
- `500`: Internal Server Error

## 🚀 Performance Considerations

### Rate Limiting
- Analysis endpoints: 10 requests per minute per user
- Progress endpoints: No limit (SSE connection)
- Metrics endpoints: 60 requests per minute per user

### Caching
- Metrics data: Cached for 5 minutes
- Token balance: Cached for 1 minute
- Analysis results: Cached indefinitely

### Pagination
- Analysis runs: Default 50 items, max 100
- Metrics timeline: Default 30 days, max 365 days

## 🧪 Testing Endpoints

### Health Check (Development)
```bash
curl -X GET "http://localhost:3000/api/tokens" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### SSE Connection Test
```javascript
const eventSource = new EventSource('/api/emails/progress?token=YOUR_TOKEN')
eventSource.onmessage = (event) => console.log(JSON.parse(event.data))
```

---

*Last Updated: January 2025*
*Status: Verified against actual implementation*
