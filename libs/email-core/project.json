{"name": "email-core", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/email-core/src", "projectType": "library", "tags": [], "targets": {"build": {"executor": "@nx/esbuild:esbuild", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"platform": "node", "outputPath": "dist/libs/email-core", "format": ["cjs"], "bundle": false, "main": "libs/email-core/src/index.ts", "tsConfig": "libs/email-core/tsconfig.lib.json", "assets": ["libs/email-core/*.md"], "esbuildOptions": {"sourcemap": true, "outExtension": {".js": ".js"}}}, "configurations": {"development": {}, "production": {"esbuildOptions": {"sourcemap": false, "outExtension": {".js": ".js"}}}}}}}