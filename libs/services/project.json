{"name": "services", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/services/src", "projectType": "library", "tags": [], "implicitDependencies": ["shared"], "targets": {"build": {"executor": "@nx/esbuild:esbuild", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"platform": "node", "outputPath": "dist/libs/services", "format": ["cjs"], "bundle": false, "main": "libs/services/src/index.ts", "tsConfig": "libs/services/tsconfig.lib.json", "assets": ["libs/services/*.md"], "esbuildOptions": {"sourcemap": true, "outExtension": {".js": ".js"}}}, "configurations": {"development": {}, "production": {"esbuildOptions": {"sourcemap": false, "outExtension": {".js": ".js"}}}}}}}