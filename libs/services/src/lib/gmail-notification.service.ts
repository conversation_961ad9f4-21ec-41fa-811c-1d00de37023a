import { Database } from './database';
import { Logger } from '../../../shared/src/lib/logger';
import { GMAIL_CONFIG } from './config';
import { AuthService } from './auth.service';
import { EmailService } from './email.service';

interface PubSubMessage {
  id: string;
  data: string;
  ack(): void;
  nack(): void;
}

interface PubSubClient {
  projectId?: string;
  subscription(name: string, options?: any): any;
}

interface WatchResponse {
  historyId: string;
  expiration: number;
}

export class GmailNotificationService {
  private emailService: EmailService;
  private authService: AuthService;
  private db: Database;
  private logger: Logger;
  private pubsub: PubSubClient;
  private subscriptionName = 'gmail-notifications-sub';
  private initialized = false;
  private projectId: string;
  private topicName: string;
  private initTimeout = 30000; // 30 seconds

  constructor(
    emailService: EmailService,
    authService: AuthService,
    database: Database,
    logger: Logger,
    pubsub: PubSubClient
  ) {
    this.emailService = emailService;
    this.authService = authService;
    this.db = database;
    this.logger = logger;
    this.pubsub = pubsub;

    // Determine project ID
    this.projectId = this._determineProjectId();
    this.topicName = GMAIL_CONFIG.pubsub.topicName;

    this.logger.info('GmailNotificationService constructor', {
      determinedProjectId: this.projectId,
      pubsubProjectId: pubsub?.projectId,
      topicName: this.topicName
    });

    if (!this.projectId) {
      this.logger.error('No project ID available in constructor');
    }

    // Initialize asynchronously
    this.initPromise = this.initialize().catch(error => {
      this.logger.error('Failed to initialize GmailNotificationService in constructor', {
        error: error instanceof Error ? error.message : String(error),
        projectId: this.projectId
      });
    });
  }

  private initPromise: Promise<void>;

  private _determineProjectId(): string {
    // Check if we're in production mode
    const isProduction = process.env['NODE_ENV'] === 'production' ||
                        process.env['USE_PRODUCTION_FIRESTORE'] === 'true';

    // Try all possible sources synchronously
    const projectId =
      // Try PubSub instance first
      (this.pubsub && this.pubsub.projectId) ||
      // Then environment variables
      process.env['GOOGLE_CLOUD_PROJECT'] ||
      process.env['PROJECT_ID'] ||
      process.env['GCLOUD_PROJECT'] ||
      // Hardcoded fallback based on environment
      (isProduction ? 'data-driven-job-search' : 'ddjs-dev-458016');

    return projectId;
  }

  async initialize(): Promise<void> {
    if (this.initialized) return;

    try {
      if (!this.projectId) {
        throw new Error('No project ID available for initialization');
      }

      this.topicName = `projects/${this.projectId}/topics/gmail-notifications`;
      this.initialized = true;

      this.logger.info('Initialized GmailNotificationService', {
        projectId: this.projectId,
        topicName: this.topicName,
        subscriptionName: this.subscriptionName
      });
    } catch (error) {
      this.logger.error('Failed to initialize GmailNotificationService', {
        error: error instanceof Error ? error.message : String(error),
        projectId: this.projectId
      });

      this.initialized = false;
      throw error;
    }
  }

  async setupWatchForUser(userId: string): Promise<WatchResponse> {
    try {
      await this.initPromise;

      if (!this.initialized) {
        throw new Error('GmailNotificationService failed to initialize');
      }

      const gmailService = await this.authService.getGmailClientForUser(userId);

      this.logger.info('Setting up watch for user', {
        userId,
        topicName: this.topicName
      });

      // Get user profile to get current historyId
      const profile = await gmailService.getUserProfile();

      // Create watch response (simplified - in real implementation would call Gmail API)
      const response: WatchResponse = {
        historyId: profile.historyId,
        expiration: Date.now() + (7 * 24 * 60 * 60 * 1000) // 7 days from now
      };

      this.logger.info('Watch response received', {
        userId,
        historyId: response.historyId,
        expiration: response.expiration,
        topicName: this.topicName
      });

      // Store watch details in Firestore
      await this.db.collection('watchDetails').doc(userId).set({
        historyId: response.historyId,
        expiration: response.expiration,
        active: true,
        lastUpdated: new Date().toISOString(),
        renewalAttempts: 0  // Reset renewal attempts on successful setup
      });

      return response;
    } catch (error) {
      this.logger.error(`Failed to setup watch for user ${userId}`, {
        error: error instanceof Error ? error.message : String(error),
        topicName: this.topicName
      });
      throw error;
    }
  }

  async stopWatchForUser(userId: string): Promise<void> {
    try {
      // Get authenticated Gmail service for user
      const gmailService = await this.authService.getGmailClientForUser(userId);

      // In real implementation, would call Gmail API to stop watch
      // await gmailService.stopWatch();

      // Update watch details in Firestore
      await this.db.collection('watchDetails').doc(userId).update({
        active: false
      });

      this.logger.info(`Watch stopped for user ${userId}`);
    } catch (error) {
      this.logger.error(`Failed to stop watch for user ${userId}`, {
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  async handleNotification(message: PubSubMessage): Promise<void> {
    try {
      const data = JSON.parse(Buffer.from(message.data, 'base64').toString());

      const {
        emailAddress,
        historyId
      } = data;

      this.logger.info('Processing notification', {
        emailAddress,
        historyId
      });

      // Get user from email address
      const userId = await this.getUserIdFromEmail(emailAddress);

      if (!userId) {
        this.logger.warn(`Received notification for unknown user: ${emailAddress}`);
        message.ack();
        return;
      }

      try {
        // Process any new emails since the last historyId
        // In real implementation, would call emailService.processNewEmails
        this.logger.info('Successfully processed emails for user', { userId, historyId });
        message.ack();
      } catch (error) {
        this.logger.error('Error processing new emails', {
          userId,
          historyId,
          error: error instanceof Error ? error.message : String(error)
        });

        // Handle specific error cases
        const errorMessage = error instanceof Error ? error.message : String(error);
        if (
          errorMessage.includes('ECONNRESET') ||
          errorMessage.includes('429') ||
          errorMessage.includes('500')
        ) {
          this.logger.info('Nacking message for retry', { userId, historyId });
          message.nack();
        } else {
          this.logger.info('Acknowledging message despite error', { userId, historyId });
          message.ack();
        }
      }
    } catch (error) {
      this.logger.error('Error handling Gmail notification', {
        error: error instanceof Error ? error.message : String(error),
        data: message.data ? Buffer.from(message.data, 'base64').toString() : null
      });
      // Ack malformed messages to prevent infinite retries
      message.ack();
    }
  }

  async getUserIdFromEmail(email: string): Promise<string | null> {
    try {
      this.logger.info('Looking up user by email', { email });

      const userDoc = await this.db.collection('users')
        .where('email', '==', email)
        .limit(1)
        .get();

      if (userDoc.empty) {
        this.logger.error('User not found in database', {
          email,
          usersCollection: 'users',
          query: `email == ${email}`
        });
        return null;
      }

      const userId = userDoc.docs[0].id;
      this.logger.info('Found user:', { email, userId });
      return userId;
    } catch (error) {
      this.logger.error('Error getting user ID from email', {
        email,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  async checkAndRenewWatches(): Promise<void> {
    try {
      const now = Date.now();
      const expiringWatches = await this.db.collection('watchDetails')
        .where('active', '==', true)
        .where('expiration', '<=', now + (24 * 60 * 60 * 1000)) // Expiring within 24 hours
        .get();

      for (const watch of expiringWatches.docs) {
        const userId = watch.id;
        this.logger.info(`Renewing watch for user ${userId}`);
        await this.setupWatchForUser(userId);
      }
    } catch (error) {
      this.logger.error('Error checking and renewing watches', {
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }
}
