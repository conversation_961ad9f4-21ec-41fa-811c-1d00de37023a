import { Database } from './database';
import { Logger } from '@webapp/shared';
import { LABEL_VALUES } from './config';

interface MetricsParams {
  userId: string;
  startDate: Date;
  endDate: Date;
  period: 'daily' | 'weekly' | 'monthly';
}

interface MetricsResult {
  labels: string[];
  applications: number[];
  interviews: number[];
}

interface MetricsData {
  applications: number;
  interviews: number;
  interviewKeys: Set<string>;
}

export class MetricsService {
  private db: Database;
  private logger: Logger;

  constructor(database: Database, logger: Logger) {
    this.db = database;
    this.logger = logger;
  }

  async getMetrics(params: MetricsParams): Promise<MetricsResult> {
    try {
      this.logger.info('Fetching metrics', {
        userId: params.userId,
        startDate: params.startDate.toISOString(),
        endDate: params.endDate.toISOString(),
        period: params.period
      });

      const analysisRef = this.db.collection('emailAnalysis');
      // Get all email analysis documents since we can't filter by userId
      // (userId is part of the document ID, not a field)
      const analysisSnapshot = await analysisRef.get();

      this.logger.info('Retrieved email analysis documents', {
        totalDocuments: analysisSnapshot.size
      });

      // Group emails by date and type
      const metrics: Record<string, MetricsData> = {};

      // Log all documents for debugging
      this.logger.debug('All documents in collection', {
        count: analysisSnapshot.size,
        docIds: analysisSnapshot.docs.map((d: any) => d.id).slice(0, 10) // Show first 10 for brevity
      });

      // Log document categories for debugging
      const categories: Record<string, number> = {};
      analysisSnapshot.docs.forEach((doc: any) => {
        const data = doc.data();
        if (data && data.parsed && data.parsed.email_type_category) {
          const category = data.parsed.email_type_category;
          categories[category] = (categories[category] || 0) + 1;
        }
      });

      this.logger.info('Email categories in collection', {
        categories,
        totalCategorized: Object.values(categories).reduce((sum, count) => sum + count, 0),
        totalDocuments: analysisSnapshot.size
      });

      analysisSnapshot.docs.forEach((doc: any) => {
        const data = doc.data();
        if (!data) {
          this.logger.debug('Skipping document with no data', { docId: doc.id });
          return; // Skip if document has no data
        }

        const docId = doc.id;

        // Extract userId from document ID (format: userId_email_messageId)
        // Handle Clerk user IDs which have format: user_[alphanumeric string]

        // Find the position of the first @ symbol which indicates the email part
        const emailStartPos = docId.indexOf('@');
        if (emailStartPos === -1) {
          this.logger.debug('Skipping document with invalid ID format (no email part)', { docId });
          return; // Skip if document ID doesn't have an email part
        }

        // Extract the user ID part (everything before the email part, minus the last underscore)
        const lastUnderscoreBeforeEmail = docId.lastIndexOf('_', emailStartPos);
        if (lastUnderscoreBeforeEmail === -1) {
          this.logger.debug('Skipping document with invalid ID format (no underscore before email)', { docId });
          return; // Skip if document ID format is invalid
        }

        const docUserId = docId.substring(0, lastUnderscoreBeforeEmail);

        this.logger.debug('Extracted user ID from document', {
          docId,
          extractedUserId: docUserId,
          requestedUserId: params.userId
        });

        if (docUserId !== params.userId) {
          this.logger.debug('Skipping document for different user', {
            docId,
            docUserId,
            requestedUserId: params.userId
          });
          return; // Skip if document doesn't belong to the requested user
        }

        // Check if document has a date field
        if (!data.date) {
          this.logger.debug('Skipping document with no date field', { docId });
          return; // Skip if document has no date field
        }

        // Skip if the date is outside our range
        // Ensure consistent date handling by standardizing to UTC
        const emailDate = new Date(data.date);

        // Create range dates with time set to start/end of day
        const rangeStart = new Date(params.startDate);
        rangeStart.setUTCHours(0, 0, 0, 0);

        const rangeEnd = new Date(params.endDate);
        rangeEnd.setUTCHours(23, 59, 59, 999);

        this.logger.debug('Document date check', {
          docId,
          emailDate: emailDate ? emailDate.toISOString() : 'invalid date',
          rangeStart: rangeStart.toISOString(),
          rangeEnd: rangeEnd.toISOString(),
          hasValidDate: emailDate && !isNaN(emailDate.getTime()),
          isInRange: emailDate && !isNaN(emailDate.getTime()) &&
                     emailDate >= rangeStart && emailDate <= rangeEnd
        });

        if (!emailDate || isNaN(emailDate.getTime()) || emailDate < rangeStart || emailDate > rangeEnd) {
          this.logger.debug('Skipping document outside date range', {
            docId,
            emailDate: emailDate ? emailDate.toISOString() : 'invalid date',
            rangeStart: rangeStart.toISOString(),
            rangeEnd: rangeEnd.toISOString()
          });
          return;
        }

        const date = this._getPeriodKey(data.date, params.period);

        this.logger.debug('Processing document for metrics', {
          docId,
          emailDate: emailDate.toISOString(),
          periodKey: date,
          category: data.parsed?.email_type_category || 'no category',
          hasParseData: !!data.parsed
        });

        if (!metrics[date]) {
          metrics[date] = {
            applications: 0,
            interviews: 0,
            interviewKeys: new Set()
          };
        }

        // Count applications - check for application-related categories
        // Use a broader set of categories that might indicate applications
        const applicationCategories = [
          'thanks_for_applying_or_application_received_confirmation',
          'inbound_job_opportunity',
          'next_steps_online_assement',
          'next_steps_interview_coordination',
          'next_steps_other'
        ];

        // Log the application categories for debugging
        this.logger.debug('Application categories for matching', {
          configCategories: LABEL_VALUES,
          applicationCategories,
          documentCategory: data.parsed?.email_type_category || 'no category'
        });

        // Check if the email has any job-related category
        const categoryMatches = applicationCategories.includes(data.parsed?.email_type_category);

        // Also check if the email is explicitly marked as job search related
        const isJobSearchRelated = data.parsed?.is_related_to_job_search === true;

        this.logger.debug('Application category check', {
          docId,
          category: data.parsed?.email_type_category || 'no category',
          isApplicationCategory: categoryMatches,
          isJobSearchRelated,
          hasParseData: !!data.parsed
        });

        // Count as application if either condition is met
        if (categoryMatches || isJobSearchRelated) {
          metrics[date].applications++;
          this.logger.debug('Counted as application', {
            docId,
            date,
            category: data.parsed?.email_type_category || 'no category',
            isJobSearchRelated,
            newCount: metrics[date].applications
          });
        }

        // Count unique interview rounds - check for interview-related categories
        const interviewCategories = [
          'interview_invitation',
          'interview_scheduling',
          'next_steps_interview_coordination',
          'interview_confirmation'
        ];

        if (interviewCategories.includes(data.parsed?.email_type_category)) {
          // Use company name from parsed data or fallback to a default
          const company = data.parsed?.company_name || data.parsed?.company || 'unknown';
          // Use interview round if available or default to 1
          const round = data.parsed?.interview_round || 1;
          const interviewKey = `${company}_${round}`;

          if (!metrics[date].interviewKeys.has(interviewKey)) {
            metrics[date].interviewKeys.add(interviewKey);
            metrics[date].interviews++;
          }
        }
      });

      // Convert to arrays for charting
      const dates = Object.keys(metrics).sort();
      const applications = dates.map(date => metrics[date].applications);
      const interviews = dates.map(date => metrics[date].interviews);

      // Create detailed metrics summary for logging
      const metricsSummary = dates.map(date => ({
        date,
        applications: metrics[date].applications,
        interviews: metrics[date].interviews
      }));

      // Calculate totals
      const totalApplications = applications.reduce((sum, val) => sum + val, 0);
      const totalInterviews = interviews.reduce((sum, val) => sum + val, 0);

      this.logger.info('Metrics data prepared', {
        userId: params.userId,
        dateCount: dates.length,
        totalApplications,
        totalInterviews,
        dateRange: {
          start: params.startDate.toISOString(),
          end: params.endDate.toISOString()
        },
        metrics: metricsSummary
      });

      // If no data was found, log a more detailed message
      if (dates.length === 0) {
        this.logger.warn('No metrics data found for the specified date range', {
          userId: params.userId,
          startDate: params.startDate.toISOString(),
          endDate: params.endDate.toISOString(),
          period: params.period,
          totalDocumentsInCollection: analysisSnapshot.size
        });
      }

      const result = {
        labels: dates,
        applications,
        interviews
      };

      this.logger.debug('Returning metrics result', {
        hasData: dates.length > 0,
        labels: result.labels,
        applications: result.applications,
        interviews: result.interviews
      });

      return result;
    } catch (error) {
      this.logger.error('Error getting metrics', {
        params,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  private _getPeriodKey(date: string, period: string): string {
    const d = new Date(date);
    switch (period) {
      case 'monthly':
        return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}`;
      case 'weekly':
        const week = Math.floor(d.getDate() / 7);
        return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}-W${week}`;
      case 'daily':
      default:
        return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}-${String(d.getDate()).padStart(2, '0')}`;
    }
  }
}
