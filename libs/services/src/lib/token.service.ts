import { Database } from './database';
import { Logger } from '../../../shared/src/lib/logger';
import { ConfigService } from './config';

export class TokenService {
  private db: Database;
  private logger: Logger;
  private config: ConfigService;

  constructor(database: Database, logger: Logger) {
    this.db = database;
    this.logger = logger;
    this.config = ConfigService.getInstance();
  }

  async initializeUserTokens(userId: string): Promise<number> {
    try {
      const initialTokens = this.config.getInitialTokenAllocation();
      console.log('TokenService: Initializing tokens for user:', userId);
      const userTokensRef = this.db.collection('userTokens').doc(userId);
      const doc = await userTokensRef.get();

      if (!doc.exists) {
        console.log('TokenService: Creating new token document with', initialTokens, 'tokens');
        await userTokensRef.set({
          remainingTokens: initialTokens,
          totalUsed: 0,
          createdAt: new Date().toISOString(),
          userId: userId
        });
        return initialTokens;
      }

      const existingTokens = doc.data()?.['remainingTokens'] || this.config.getInitialTokenAllocation();
      console.log('TokenService: User already has token document with', existingTokens, 'tokens');
      return existingTokens;
    } catch (error) {
      console.error('TokenService: Error initializing user tokens', { userId, error });
      this.logger.error('Error initializing user tokens', { userId, error: error instanceof Error ? error.message : String(error) });
      throw error;
    }
  }

  async getRemainingTokens(userId: string): Promise<number> {
    try {
      console.log('TokenService: Getting tokens for user:', userId);
      const doc = await this.db.collection('userTokens').doc(userId).get();
      console.log('TokenService: Document exists:', doc.exists);

      if (!doc.exists) {
        console.log('TokenService: No document found, initializing user tokens');
        return await this.initializeUserTokens(userId);
      }

      const data = doc.data();
      console.log('TokenService: Document data:', data);
      return data?.['remainingTokens'] || 0;
    } catch (error) {
      console.error('TokenService: Error getting remaining tokens', { userId, error });
      this.logger.error('Error getting remaining tokens', { userId, error: error instanceof Error ? error.message : String(error) });
      throw error;
    }
  }

  async deductTokens(userId: string, amount: number): Promise<number> {
    const ref = this.db.collection('userTokens').doc(userId);

    return this.db.runTransaction(async (transaction) => {
      const doc = await transaction.get(ref);
      const data = doc.data();
      const currentTokens = data?.['remainingTokens'] || 0;

      if (currentTokens < amount) {
        this.logger.error('Insufficient tokens', { userId, currentTokens, requested: amount });
        throw new Error('Insufficient tokens');
      }

      transaction.update(ref, {
        remainingTokens: currentTokens - amount,
        totalUsed: (data?.['totalUsed'] || 0) + amount
      });

      this.logger.info('Tokens deducted successfully', {
        userId,
        amount,
        remaining: currentTokens - amount
      });

      return currentTokens - amount;
    }).catch(error => {
      this.logger.error('Error deducting tokens', { userId, amount, error: error instanceof Error ? error.message : String(error) });
      throw error;
    });
  }
}
