import { Database } from './database';
import { Logger } from '../../../shared/src/lib/logger';
import { AuthService } from './auth.service';

interface NotificationPreferences {
  automaticAnalysis: boolean;
  skipInbox: {
    confirmations: boolean;
    rejections: boolean;
  };
}

interface GmailNotificationService {
  setupWatchForUser(userId: string): Promise<any>;
  stopWatchForUser(userId: string): Promise<any>;
}

export class NotificationPreferencesService {
  private authService: AuthService;
  private db: Database;
  private logger: Logger;
  private gmailNotificationService: GmailNotificationService;
  private preferencesCollection = 'notificationPreferences';

  constructor(
    authService: AuthService,
    database: Database,
    logger: Logger,
    gmailNotificationService: GmailNotificationService
  ) {
    this.authService = authService;
    this.db = database;
    this.logger = logger;
    this.gmailNotificationService = gmailNotificationService;
  }

  async getPreferences(userId: string): Promise<NotificationPreferences> {
    try {
      const doc = await this.db.collection(this.preferencesCollection)
        .doc(userId)
        .get();

      if (!doc.exists) {
        // Default preferences
        return {
          automaticAnalysis: false,
          skipInbox: {
            confirmations: false,
            rejections: false
          }
        };
      }

      return doc.data() as NotificationPreferences;
    } catch (error) {
      this.logger.error('Error getting notification preferences', {
        userId,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  async updatePreferences(userId: string, preferences: NotificationPreferences): Promise<NotificationPreferences> {
    try {
      // Check if user is authenticated (simplified check)
      // In a real implementation, you'd verify the user's authentication status

      // If enabling automatic analysis, set up Gmail watch
      if (preferences.automaticAnalysis) {
        await this.gmailNotificationService.setupWatchForUser(userId);
      } else {
        await this.gmailNotificationService.stopWatchForUser(userId);
      }

      await this.db.collection(this.preferencesCollection)
        .doc(userId)
        .set(preferences, { merge: true });

      return preferences;
    } catch (error) {
      this.logger.error(`Error updating notification preferences: ${error instanceof Error ? error.message : String(error)}`, {
        userId,
        error: error instanceof Error ? error.stack : String(error)
      });
      throw error;
    }
  }
}
