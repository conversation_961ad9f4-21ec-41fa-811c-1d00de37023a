import { google } from 'googleapis';
import { Base64 } from 'js-base64';
import { DDJS_LABEL_PREFIX } from './config';
import { Logger } from '../../../shared/src/lib/logger';

interface EmailContent {
  text: string;
  date: string;
}

interface HistoryChange {
  id: string;
  messages?: any[];
  messagesAdded?: any[];
}

export class GmailService {
  private logger: Logger;
  private gmail: any;
  private monitoredEmail: string | null = null;

  constructor(auth: any, logger: Logger) {
    this.logger = logger;

    this.logger.info('🔍 GMAIL SERVICE DEBUG: Initializing Gmail service constructor', {
      hasAuth: !!auth,
      authType: typeof auth,
      authKeys: auth ? Object.keys(auth) : []
    });

    // Validate auth object
    if (!auth) {
      this.logger.error('🚨 GMAIL SERVICE ERROR: Auth object is required');
      throw new Error('Auth object is required to initialize Gmail service');
    }

    this.logger.info('🔍 GMAIL SERVICE DEBUG: Auth object validation', {
      hasCredentials: !!auth.credentials,
      credentialsType: typeof auth.credentials,
      credentialsKeys: auth.credentials ? Object.keys(auth.credentials) : [],
      hasAccessToken: !!auth.credentials?.access_token,
      accessTokenType: typeof auth.credentials?.access_token,
      accessTokenLength: auth.credentials?.access_token ? auth.credentials.access_token.length : 0,
      hasTokenType: !!auth.credentials?.token_type,
      tokenType: auth.credentials?.token_type,
      hasScope: !!auth.credentials?.scope,
      scope: auth.credentials?.scope
    });

    // Validate credentials
    if (!auth.credentials || !auth.credentials.access_token) {
      this.logger.error('🚨 GMAIL SERVICE ERROR: Invalid credentials', {
        hasCredentials: !!auth.credentials,
        hasAccessToken: !!auth.credentials?.access_token
      });
      throw new Error('Auth object must contain valid credentials with access_token');
    }

    try {
      this.logger.info('🔍 GMAIL SERVICE DEBUG: Creating Google Gmail client');
      this.gmail = google.gmail({ version: 'v1', auth });
      this.logger.info('✅ GMAIL SERVICE SUCCESS: Gmail client created successfully');
    } catch (error) {
      this.logger.error('🚨 GMAIL SERVICE ERROR: Failed to create Gmail client', {
        error: error instanceof Error ? error.message : String(error),
        errorStack: error instanceof Error ? error.stack : undefined
      });
      throw new Error('Failed to initialize Gmail client: ' + (error instanceof Error ? error.message : String(error)));
    }
  }

  async initialize(): Promise<void> {
    try {
      this.logger.info('Initializing Gmail service');

      // Test the auth by making a simple API call
      await this._testAuth();

      // Get user profile using Gmail API
      const response = await this.gmail.users.getProfile({
        userId: 'me'
      });

      this.monitoredEmail = response.data.emailAddress;
      this.logger.info('Gmail service initialized successfully', {
        email: this.monitoredEmail,
        historyId: response.data.historyId,
        messagesTotal: response.data.messagesTotal,
        threadsTotal: response.data.threadsTotal
      });
    } catch (error) {
      this.logger.error('Failed to initialize Gmail service', {
        error: error instanceof Error ? error.message : String(error)
      });
      throw new Error('Gmail service initialization failed: ' + (error instanceof Error ? error.message : String(error)));
    }
  }

  private async _testAuth(): Promise<void> {
    try {
      this.logger.info('🔍 GMAIL API DEBUG: Making API call to test Gmail authentication');
      const response = await this.gmail.users.labels.list({
        userId: 'me'
      });

      this.logger.info('✅ GMAIL API SUCCESS: Gmail API authentication test successful', {
        responseStatus: response.status,
        labelsCount: response.data.labels?.length,
        hasLabels: !!response.data.labels,
        responseKeys: response.data ? Object.keys(response.data) : []
      });
    } catch (error: any) {
      this.logger.error('🚨 GMAIL API ERROR: Gmail API authentication test failed', {
        error: error instanceof Error ? error.message : String(error),
        errorCode: error.code,
        errorStatus: error.status,
        errorResponse: error.response?.data,
        errorStack: error instanceof Error ? error.stack : undefined
      });
      throw new Error('Gmail API authentication test failed: ' + (error instanceof Error ? error.message : String(error)));
    }
  }

  getMonitoredEmail(): string {
    if (!this.monitoredEmail) {
      throw new Error('GmailService not initialized - call initialize() first');
    }
    return this.monitoredEmail;
  }

  async getEmailsByDateRange(startDate: string, endDate: string): Promise<any[]> {
    try {
      let pageToken = '';
      let allMessages: any[] = [];

      do {
        const response = await this.gmail.users.messages.list({
          userId: 'me',
          q: `after:${startDate} before:${endDate}`,
          maxResults: 100,
          pageToken: pageToken
        });

        allMessages = allMessages.concat(response.data.messages || []);
        pageToken = response.data.nextPageToken;
      } while (pageToken);

      return allMessages;
    } catch (error) {
      this.logger.error('Failed to fetch emails', { error: error instanceof Error ? error.message : String(error) });
      throw error;
    }
  }

  async getEmailContent(emailId: string): Promise<EmailContent | null> {
    try {
      const res = await this.gmail.users.messages.get({
        userId: 'me',
        id: emailId
      });

      const emailDate = new Date(parseInt(res.data.internalDate)).toISOString();
      const content = this._extractEmailContent(res.data.payload);

      if (!content) {
        throw new Error('Failed to decode email content');
      }

      return { text: content, date: emailDate };
    } catch (error: any) {
      if (error.response?.status === 404) {
        this.logger.warn('Email not found - may have been deleted', { emailId });
        return null;
      }

      throw error;
    }
  }

  async getDDJSLabelIds(): Promise<Record<string, string>> {
    const response = await this.gmail.users.labels.list({ userId: 'me' });
    const labels = response.data.labels || [];

    return labels
      .filter((label: any) => label.name.startsWith(DDJS_LABEL_PREFIX))
      .reduce((acc: Record<string, string>, label: any) => {
        acc[label.name] = label.id;
        return acc;
      }, {});
  }

  async createLabel(name: string): Promise<any> {
    return await this.gmail.users.labels.create({
      userId: 'me',
      requestBody: {
        name: name,
        labelListVisibility: 'labelShow',
        messageListVisibility: 'show'
      }
    });
  }

  async addLabel(messageId: string, labelId: string): Promise<any> {
    return await this.gmail.users.messages.modify({
      userId: 'me',
      id: messageId,
      requestBody: {
        addLabelIds: [labelId]
      }
    });
  }

  private _extractEmailContent(payload: any): string | null {
    if (!payload) {
      this.logger.error('No payload provided for email content extraction');
      return null;
    }

    // Helper function to find text content in parts
    const findTextContent = (parts: any[]): { plainText: string | null; htmlText: string | null } => {
      let plainText = null;
      let htmlText = null;

      for (const part of parts) {
        if (part.mimeType === 'text/plain' && part.body?.data) {
          plainText = Base64.decode(part.body.data);
        } else if (part.mimeType === 'text/html' && part.body?.data) {
          htmlText = Base64.decode(part.body.data);
        } else if (part.parts) {
          // Recursively check nested parts
          const nestedContent = findTextContent(part.parts);
          if (nestedContent.plainText) plainText = nestedContent.plainText;
          if (nestedContent.htmlText) htmlText = nestedContent.htmlText;
        }
      }

      return { plainText, htmlText };
    };

    // Handle direct body content
    if (payload.body?.data) {
      return Base64.decode(payload.body.data);
    }

    // Handle multipart content
    if (payload.parts) {
      const { plainText, htmlText } = findTextContent(payload.parts);

      // Prioritize plain text over HTML
      return plainText || htmlText;
    }

    this.logger.error('No suitable content found in email', {
      mimeType: payload.mimeType,
      hasBody: !!payload.body,
      partsCount: payload.parts?.length
    });

    return null;
  }

  async getHistory(startHistoryId: string, endHistoryId?: string): Promise<any> {
    try {
      this.logger.info('Fetching history', { startHistoryId, endHistoryId });

      const requestParams: any = {
        userId: 'me',
        startHistoryId: startHistoryId,
        historyTypes: ['messageAdded'],
        maxResults: 100
      };

      // If endHistoryId is provided, we'll fetch all history up to that point
      // Note: Gmail API doesn't have endHistoryId parameter, so we'll fetch and filter
      const response = await this.gmail.users.history.list(requestParams);

      let history = response.data.history || [];

      // If endHistoryId is specified, filter history to only include records up to that point
      if (endHistoryId && history.length > 0) {
        const endHistoryIdNum = parseInt(endHistoryId, 10);
        history = history.filter((record: any) => {
          const recordHistoryId = parseInt(record.id, 10);
          return recordHistoryId <= endHistoryIdNum;
        });
      }

      this.logger.info('History fetched successfully', {
        startHistoryId,
        endHistoryId,
        recordCount: history.length
      });

      return { history };
    } catch (error: any) {
      if (error.code === 404) {
        // Handle expired historyId
        this.logger.warn('History ID expired, full sync may be required', { startHistoryId });
        return { history: [] };
      }

      this.logger.error('Error fetching history', {
        startHistoryId,
        endHistoryId,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  async getUserProfile(): Promise<any> {
    try {
      const response = await this.gmail.users.getProfile({
        userId: 'me'
      });
      return response.data;
    } catch (error) {
      this.logger.error('Failed to get user profile', { error: error instanceof Error ? error.message : String(error) });
      throw error;
    }
  }

  async modifyMessage(messageId: string, options: { addLabelIds?: string[]; removeLabelIds?: string[] } = {}): Promise<void> {
    try {
      const { addLabelIds = [], removeLabelIds = [] } = options;
      const requestBody: any = {};

      if (addLabelIds.length > 0) {
        requestBody.addLabelIds = addLabelIds;
      }

      if (removeLabelIds.length > 0) {
        requestBody.removeLabelIds = removeLabelIds;
      }

      await this.gmail.users.messages.modify({
        userId: 'me',
        id: messageId,
        requestBody
      });

      this.logger.info('Message modified successfully', {
        messageId,
        addLabelIds,
        removeLabelIds
      });
    } catch (error) {
      this.logger.error('Failed to modify message', {
        messageId,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }
}
