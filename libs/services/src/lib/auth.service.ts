import { createClerkClient } from '@clerk/backend';
import { google } from 'googleapis';
import { Logger } from '../../../shared/src/lib/logger';
import { GmailService } from './gmail.service';
import { SecretManagerService } from './secret-manager.service';

interface UserEmail {
  address: string;
  verified: boolean;
  primary: boolean;
}

export class AuthService {
  private logger: Logger;
  private clerkClient: any;
  private secretManager: SecretManagerService;
  private isInitialized: boolean = false;

  constructor(logger: Logger) {
    this.logger = logger;
    this.secretManager = new SecretManagerService();

    // Initialize clerk client - will be done async in initialize()
    this.clerkClient = null;
  }

  /**
   * Initialize the AuthService by retrieving secrets from Secret Manager
   * This must be called before using any other methods
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      return; // Already initialized
    }

    try {
      this.logger.info('🔍 AUTH SERVICE DEBUG: Initializing AuthService with Secret Manager');

      // Try to get from environment variable first (for local development)
      let clerkS<PERSON>retKey = process.env['CLERK_SECRET_KEY'];

      if (!clerkSecretKey) {
        this.logger.info('🔍 AUTH SERVICE DEBUG: CLERK_SECRET_KEY not in environment, retrieving from Secret Manager');
        clerkSecretKey = await this.secretManager.getClerkSecretKey();
        this.logger.info('✅ AUTH SERVICE SUCCESS: Retrieved CLERK_SECRET_KEY from Secret Manager');
      } else {
        this.logger.info('🔍 AUTH SERVICE DEBUG: Using CLERK_SECRET_KEY from environment variable');
      }

      if (!clerkSecretKey) {
        throw new Error('Missing Clerk Secret Key. Go to https://dashboard.clerk.com and get your key for your instance.');
      }

      // Initialize clerk client with secret key
      this.clerkClient = createClerkClient({
        secretKey: clerkSecretKey
      });

      this.isInitialized = true;
      this.logger.info('✅ AUTH SERVICE SUCCESS: AuthService initialized successfully');
    } catch (error) {
      this.logger.error('🚨 AUTH SERVICE ERROR: Failed to initialize AuthService', {
        error: error instanceof Error ? error.message : String(error),
        errorStack: error instanceof Error ? error.stack : undefined
      });
      throw new Error('Gmail API authentication failed: ' + (error instanceof Error ? error.message : String(error)));
    }
  }

  /**
   * Ensure the service is initialized before use
   */
  private async ensureInitialized(): Promise<void> {
    if (!this.isInitialized) {
      await this.initialize();
    }
  }

  async getClerkUserIdByEmail(email: string): Promise<string> {
    await this.ensureInitialized();

    try {
      this.logger.info('Fetching Clerk user by email', { email });
      const users = await this.clerkClient.users.getUserList({
        emailAddress: [email],
        limit: 1
      });

      if (!users || users.length === 0) {
        throw new Error('No user found with that email');
      }

      const userId = users[0].id;
      this.logger.info('Found Clerk user', { email, userId });
      return userId;
    } catch (error) {
      this.logger.error('Error fetching user by email:', {
        email,
        error: error instanceof Error ? error.message : String(error)
      });
      throw new Error('Could not retrieve user information');
    }
  }

  /**
   * Creates a Gmail service client for a user using Clerk-managed OAuth tokens.
   *
   * IMPORTANT: This method uses Clerk's OAuth token management, which eliminates
   * the need for GOOGLE_CLIENT_ID and GOOGLE_CLIENT_SECRET environment variables.
   *
   * Authentication Flow:
   * 1. User authenticates with Google through Clerk's OAuth flow
   * 2. Clerk stores and manages OAuth tokens (access + refresh)
   * 3. We retrieve tokens from Clerk using getUserOauthAccessToken()
   * 4. We create a Gmail client using the retrieved tokens
   *
   * No direct OAuth client credentials are required in our application.
   */
  async getGmailClientForUser(userId: string): Promise<GmailService> {
    await this.ensureInitialized();

    try {
      this.logger.info('🔍 GMAIL AUTH DEBUG: Starting Gmail client creation', { userId });

      // First check if the user has connected their Google account
      this.logger.info('🔍 GMAIL AUTH DEBUG: Fetching user from Clerk', { userId });
      const user = await this.clerkClient.users.getUser(userId);

      this.logger.info('🔍 GMAIL AUTH DEBUG: User fetched successfully', {
        userId,
        hasExternalAccounts: !!user.externalAccounts,
        externalAccountsCount: user.externalAccounts?.length || 0,
        externalAccountProviders: user.externalAccounts?.map((acc: any) => acc.provider) || []
      });

      const googleAccount = user.externalAccounts.find(
        (account: any) => account.provider === 'oauth_google'
      );

      if (!googleAccount) {
        this.logger.error('🚨 GMAIL AUTH ERROR: No Google account connected', {
          userId,
          availableProviders: user.externalAccounts?.map((acc: any) => acc.provider) || [],
          totalExternalAccounts: user.externalAccounts?.length || 0
        });
        throw new Error('User has not connected their Google account');
      }

      this.logger.info('🔍 GMAIL AUTH DEBUG: Google account found', {
        userId,
        googleAccountId: googleAccount.id,
        googleAccountEmail: googleAccount.emailAddress,
        hasVerification: !!googleAccount.verification,
        verificationStatus: googleAccount.verification?.status,
        scopes: googleAccount.verification?.scopes || [],
        scopesCount: googleAccount.verification?.scopes?.length || 0
      });

      // Get OAuth token using the Clerk OAuth tokens API
      this.logger.info('🔍 GMAIL AUTH DEBUG: Requesting OAuth token from Clerk', { userId });
      const tokenResponse = await this.clerkClient.users.getUserOauthAccessToken(userId, 'google');

      this.logger.info('🔍 GMAIL AUTH DEBUG: OAuth token response received', {
        userId,
        hasTokenResponse: !!tokenResponse,
        hasData: !!tokenResponse?.data,
        dataLength: tokenResponse?.data?.length || 0,
        responseKeys: tokenResponse ? Object.keys(tokenResponse) : [],
        dataStructure: tokenResponse?.data ? tokenResponse.data.map((item: any, index: number) => ({
          index,
          hasToken: !!item?.token,
          tokenType: typeof item?.token,
          tokenKeys: item?.token && typeof item.token === 'object' ? Object.keys(item.token) : []
        })) : []
      });

      if (!tokenResponse || !tokenResponse.data || tokenResponse.data.length === 0) {
        this.logger.error('🚨 GMAIL AUTH ERROR: Failed to get OAuth token', {
          userId,
          tokenResponse: tokenResponse ? 'exists' : 'null',
          hasData: !!tokenResponse?.data,
          dataLength: tokenResponse?.data?.length || 0
        });
        throw new Error('Failed to get OAuth token from Clerk');
      }

      // Clerk v6.x returns a response with data array containing tokens
      const token = tokenResponse.data[0]?.token;

      if (!token) {
        this.logger.error('🚨 GMAIL AUTH ERROR: Token is missing in response', {
          userId,
          dataItem: tokenResponse.data[0],
          dataItemKeys: tokenResponse.data[0] ? Object.keys(tokenResponse.data[0]) : []
        });
        throw new Error('Invalid token format from Clerk');
      }

      // Get scopes from verification
      const scopes = googleAccount.verification?.scopes || [];

      this.logger.info('🔍 GMAIL AUTH DEBUG: Token extracted successfully', {
        userId,
        tokenType: typeof token,
        tokenLength: typeof token === 'string' ? token.length : 'N/A',
        tokenKeys: typeof token === 'object' ? Object.keys(token) : [],
        scopes,
        scopesCount: scopes.length,
        hasGmailScope: scopes.includes('https://www.googleapis.com/auth/gmail.readonly')
      });

      // Create OAuth2 client with the token
      // Note: No client_id/client_secret needed because we're using pre-authorized tokens from Clerk
      const oauth2Client = new google.auth.OAuth2();

      // Handle token format - Clerk returns either a string or an object
      let credentials: any;
      if (typeof token === 'string') {
        credentials = {
          access_token: token,
          token_type: 'Bearer',
          scope: scopes.join(' ')
        };
      } else if (typeof token === 'object') {
        credentials = {
          access_token: token.access_token,
          token_type: token.token_type || 'Bearer',
          scope: scopes.join(' ')
        };
      } else {
        throw new Error(`Unexpected token format: ${typeof token}`);
      }

      this.logger.info('🔍 GMAIL AUTH DEBUG: Setting OAuth2 credentials', {
        userId,
        hasAccessToken: !!credentials.access_token,
        accessTokenLength: credentials.access_token ? credentials.access_token.length : 0,
        tokenType: credentials.token_type,
        scope: credentials.scope,
        credentialsKeys: Object.keys(credentials)
      });

      oauth2Client.setCredentials(credentials);

      this.logger.info('🔍 GMAIL AUTH DEBUG: Creating Gmail service', { userId });
      const gmailService = new GmailService(oauth2Client, this.logger);

      this.logger.info('🔍 GMAIL AUTH DEBUG: Initializing Gmail service', { userId });
      await gmailService.initialize();

      this.logger.info('✅ GMAIL AUTH SUCCESS: Gmail service initialized successfully', {
        userId,
        monitoredEmail: gmailService.getMonitoredEmail()
      });

      return gmailService;
    } catch (error) {
      this.logger.error('🚨 GMAIL AUTH FATAL ERROR: Failed to get Gmail client', {
        userId,
        errorMessage: error instanceof Error ? error.message : String(error),
        errorStack: error instanceof Error ? error.stack : undefined,
        errorType: error instanceof Error ? error.constructor.name : typeof error
      });
      throw new Error('Gmail API authentication failed: ' + (error instanceof Error ? error.message : String(error)));
    }
  }

  async getGmailClientForEmail(email: string): Promise<GmailService> {
    try {
      const userId = await this.getClerkUserIdByEmail(email);
      return this.getGmailClientForUser(userId);
    } catch (error) {
      this.logger.error('Failed to get Gmail client by email', {
        email,
        error: error instanceof Error ? error.message : String(error)
      });
      throw new Error(`Gmail access failed for ${email}`);
    }
  }

  async getUserEmails(userId: string): Promise<UserEmail[]> {
    await this.ensureInitialized();

    try {
      const user = await this.clerkClient.users.getUser(userId);
      return user.emailAddresses
        .filter((e: any) => e.verification?.status === 'verified')
        .map((e: any) => ({
          address: e.emailAddress,
          verified: true,
          primary: e.id === user.primaryEmailAddressId
        }));
    } catch (error) {
      this.logger.error('Failed to get user emails', {
        userId,
        error: error instanceof Error ? error.message : String(error)
      });
      throw new Error('Could not retrieve user email information');
    }
  }
}
