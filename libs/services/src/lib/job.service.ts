import { Database, Logger, BatchQueryService, sanitizeId } from '@webapp/shared';
import { v4 as uuidv4 } from 'uuid';

/**
 * Service for managing analysis jobs
 * Handles job creation, status updates, and querying
 */

export interface AnalysisJob {
  id: string;
  clerkUserId: string;
  sourceType: 'email' | 'linkedin' | 'indeed';
  status: 'idle' | 'processing' | 'completed' | 'error';
  progress: {
    processed: number;
    total: number;
    current: string;
  };
  createdAt: string;
  updatedAt: string;
  startDate?: string;
  endDate?: string;
  results?: any;
  error?: string;
  metadata?: {
    cachedCount?: number;
    newAnalysisCount?: number;
    remainingTokens?: number;
    estimatedTotal?: number;
  };
}

export interface JobFilters {
  sourceType?: string;
  status?: string;
  startDate?: string;
  endDate?: string;
}

export interface JobListOptions {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export class JobService {
  private db: Database;
  private logger: Logger;
  private batchService: BatchQueryService;

  constructor() {
    this.db = new Database();
    this.logger = new Logger();
    this.batchService = new BatchQueryService();
  }

  /**
   * Create a new analysis job
   */
  async createJob(
    clerkUserId: string,
    sourceType: AnalysisJob['sourceType'],
    options: {
      startDate?: string;
      endDate?: string;
      metadata?: AnalysisJob['metadata'];
    } = {}
  ): Promise<AnalysisJob> {
    try {
      const jobId = uuidv4();
      const now = new Date().toISOString();
      
      const job: AnalysisJob = {
        id: jobId,
        clerkUserId: sanitizeId(clerkUserId),
        sourceType,
        status: 'idle',
        progress: {
          processed: 0,
          total: 0,
          current: 'Job created'
        },
        createdAt: now,
        updatedAt: now,
        startDate: options.startDate,
        endDate: options.endDate,
        metadata: options.metadata
      };

      await this.db.collection('analysisJobs').doc(jobId).set(job);
      
      this.logger.info('Analysis job created', {
        jobId,
        clerkUserId,
        sourceType
      });

      return job;
    } catch (error) {
      this.logger.error('Failed to create analysis job', {
        clerkUserId,
        sourceType,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * Update job status and progress
   */
  async updateJob(
    jobId: string,
    updates: Partial<Pick<AnalysisJob, 'status' | 'progress' | 'results' | 'error' | 'metadata'>>
  ): Promise<void> {
    try {
      const updateData = {
        ...updates,
        updatedAt: new Date().toISOString()
      };

      await this.db.collection('analysisJobs').doc(jobId).update(updateData);
      
      this.logger.debug('Analysis job updated', {
        jobId,
        status: updates.status,
        processed: updates.progress?.processed,
        total: updates.progress?.total
      });
    } catch (error) {
      this.logger.error('Failed to update analysis job', {
        jobId,
        updates,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * Get a specific job by ID
   */
  async getJob(jobId: string, clerkUserId?: string): Promise<AnalysisJob | null> {
    try {
      const doc = await this.db.collection('analysisJobs').doc(jobId).get();
      
      if (!doc.exists) {
        return null;
      }

      const job = { id: doc.id, ...doc.data() } as AnalysisJob;
      
      // Verify user access if clerkUserId provided
      if (clerkUserId && job.clerkUserId !== sanitizeId(clerkUserId)) {
        this.logger.warn('Unauthorized job access attempt', {
          jobId,
          requestedBy: clerkUserId,
          jobOwner: job.clerkUserId
        });
        return null;
      }

      return job;
    } catch (error) {
      this.logger.error('Failed to get analysis job', {
        jobId,
        clerkUserId,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * Get multiple jobs by IDs (batch operation)
   */
  async getJobs(jobIds: string[], clerkUserId?: string): Promise<AnalysisJob[]> {
    try {
      const results = await this.batchService.getMultipleJobStatuses(jobIds);
      
      const jobs = results
        .map(result => ({ id: result.id, ...result.data } as AnalysisJob))
        .filter(job => {
          // Filter by user access if clerkUserId provided
          if (clerkUserId) {
            return job.clerkUserId === sanitizeId(clerkUserId);
          }
          return true;
        });

      return jobs;
    } catch (error) {
      this.logger.error('Failed to get analysis jobs batch', {
        jobIds,
        clerkUserId,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * List jobs for a user with filtering and pagination
   */
  async listJobs(
    clerkUserId: string,
    filters: JobFilters = {},
    options: JobListOptions = {}
  ): Promise<{
    jobs: AnalysisJob[];
    total: number;
    page: number;
    totalPages: number;
    hasMore: boolean;
  }> {
    try {
      const sanitizedUserId = sanitizeId(clerkUserId);
      const page = options.page || 1;
      const limit = Math.min(options.limit || 20, 100); // Max 100 per page
      const offset = (page - 1) * limit;

      // Use batch service for optimized querying
      const batchFilters = {
        sourceType: filters.sourceType,
        status: filters.status,
        limit,
        offset,
        sortBy: options.sortBy || 'createdAt',
        sortOrder: options.sortOrder || 'desc'
      };

      const result = await this.batchService.getAnalysisRunsBatch(
        sanitizedUserId,
        batchFilters
      );

      const totalPages = Math.ceil(result.total / limit);
      const hasMore = page < totalPages;

      this.logger.debug('Listed analysis jobs', {
        clerkUserId,
        filters,
        page,
        limit,
        total: result.total,
        returned: result.runs.length
      });

      return {
        jobs: result.runs as AnalysisJob[],
        total: result.total,
        page,
        totalPages,
        hasMore
      };
    } catch (error) {
      this.logger.error('Failed to list analysis jobs', {
        clerkUserId,
        filters,
        options,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * Get active jobs for a user (for polling)
   */
  async getActiveJobs(clerkUserId: string): Promise<AnalysisJob[]> {
    try {
      const result = await this.listJobs(
        clerkUserId,
        { status: 'processing' },
        { limit: 50, sortBy: 'updatedAt', sortOrder: 'desc' }
      );

      return result.jobs;
    } catch (error) {
      this.logger.error('Failed to get active jobs', {
        clerkUserId,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * Mark job as completed with results
   */
  async completeJob(jobId: string, results: any, metadata?: AnalysisJob['metadata']): Promise<void> {
    try {
      await this.updateJob(jobId, {
        status: 'completed',
        results,
        metadata,
        progress: {
          processed: metadata?.estimatedTotal || 0,
          total: metadata?.estimatedTotal || 0,
          current: 'Analysis completed'
        }
      });

      this.logger.info('Analysis job completed', {
        jobId,
        resultsSize: JSON.stringify(results).length,
        metadata
      });
    } catch (error) {
      this.logger.error('Failed to complete analysis job', {
        jobId,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * Mark job as failed with error
   */
  async failJob(jobId: string, error: string): Promise<void> {
    try {
      await this.updateJob(jobId, {
        status: 'error',
        error,
        progress: {
          processed: 0,
          total: 0,
          current: `Error: ${error}`
        }
      });

      this.logger.error('Analysis job failed', {
        jobId,
        error
      });
    } catch (updateError) {
      this.logger.error('Failed to mark job as failed', {
        jobId,
        originalError: error,
        updateError: updateError instanceof Error ? updateError.message : String(updateError)
      });
      throw updateError;
    }
  }

  /**
   * Delete old completed jobs (cleanup)
   */
  async cleanupOldJobs(olderThanDays: number = 30): Promise<number> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);
      const cutoffIso = cutoffDate.toISOString();

      const snapshot = await this.db
        .collection('analysisJobs')
        .where('status', 'in', ['completed', 'error'])
        .where('updatedAt', '<', cutoffIso)
        .limit(100) // Process in batches
        .get();

      if (snapshot.empty) {
        return 0;
      }

      const batch = this.db.db.batch();
      snapshot.docs.forEach(doc => {
        batch.delete(doc.ref);
      });

      await batch.commit();

      this.logger.info('Cleaned up old analysis jobs', {
        count: snapshot.size,
        olderThanDays,
        cutoffDate: cutoffIso
      });

      return snapshot.size;
    } catch (error) {
      this.logger.error('Failed to cleanup old jobs', {
        olderThanDays,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * Get job statistics for a user
   */
  async getJobStats(clerkUserId: string): Promise<{
    total: number;
    byStatus: Record<string, number>;
    bySourceType: Record<string, number>;
    recentActivity: number; // Jobs in last 7 days
  }> {
    try {
      const sanitizedUserId = sanitizeId(clerkUserId);
      
      // Get all jobs for the user
      const allJobs = await this.listJobs(sanitizedUserId, {}, { limit: 1000 });
      
      const sevenDaysAgo = new Date();
      sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
      const sevenDaysAgoIso = sevenDaysAgo.toISOString();

      const stats = {
        total: allJobs.total,
        byStatus: {} as Record<string, number>,
        bySourceType: {} as Record<string, number>,
        recentActivity: 0
      };

      allJobs.jobs.forEach(job => {
        // Count by status
        stats.byStatus[job.status] = (stats.byStatus[job.status] || 0) + 1;
        
        // Count by source type
        stats.bySourceType[job.sourceType] = (stats.bySourceType[job.sourceType] || 0) + 1;
        
        // Count recent activity
        if (job.createdAt >= sevenDaysAgoIso) {
          stats.recentActivity++;
        }
      });

      return stats;
    } catch (error) {
      this.logger.error('Failed to get job stats', {
        clerkUserId,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }
}
