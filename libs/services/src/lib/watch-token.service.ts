import { Database } from './database';
import { Logger } from '@webapp/shared';

interface WatchDetails {
  userId: string;
  expiration: number;
  renewalAttempts: number;
  active: boolean;
  lastAttempt?: string;
  [key: string]: any;
}

interface ExpiringToken extends WatchDetails {
  userId: string;
}

interface UserWatch {
  email: string;
  userId: string;
  active: boolean;
  [key: string]: any;
}

export class WatchTokenService {
  private db: Database;
  private logger: Logger;
  private collection = 'watchDetails';

  constructor(database: Database, logger: Logger) {
    this.db = database;
    this.logger = logger;
  }

  async storeWatchToken(userId: string, email: string, watchDetails: any): Promise<void> {
    await this.db.collection('userEmails').doc(email).set({
      userId,
      ...watchDetails,
      active: true
    });
  }

  async getExpiringTokens(hoursThreshold: number = 24): Promise<ExpiringToken[]> {
    const expirationThreshold = Date.now() + (hoursThreshold * 60 * 60 * 1000);

    try {
      this.logger.info('Fetching expiring watch tokens', {
        threshold: new Date(expirationThreshold).toISOString(),
        hoursThreshold
      });

      const snapshot = await this.db.collection('watchDetails')
        .where('active', '==', true)
        .where('expiration', '<=', expirationThreshold)
        .where('renewalAttempts', '<', 3)
        .orderBy('expiration', 'asc')
        .get();

      const tokens: ExpiringToken[] = snapshot.docs.map(doc => ({
        userId: doc.id,
        ...doc.data()
      } as ExpiringToken));

      this.logger.info('Found expiring watch tokens', {
        count: tokens.length,
        tokens: tokens.map(t => ({
          userId: t.userId,
          expiration: t.expiration,
          attempts: t.renewalAttempts
        }))
      });

      return tokens;
    } catch (error: any) {
      if (error.code === 9 && error.message.includes('requires an index')) {
        this.logger.error('Missing required Firestore index', {
          error: error.message,
          collection: this.collection,
          indexUrl: error.message.match(/https:\/\/console\.firebase\.google\.com[^\s]*/)?.[0]
        });
        throw new Error('Database index not ready. Please create the required index and try again.');
      }

      this.logger.error('Failed to get expiring tokens', {
        error: error.message,
        stack: error.stack,
        collection: this.collection,
        threshold: new Date(expirationThreshold).toISOString()
      });
      throw error;
    }
  }

  async updateRenewalAttempts(userId: string): Promise<void> {
    try {
      const ref = this.db.collection(this.collection).doc(userId);
      await this.db.runTransaction(async (transaction) => {
        const doc = await transaction.get(ref);
        if (!doc.exists) {
          this.logger.warn('Watch details document not found for user', { userId });
          return;
        }

        const data = doc.data();
        const attempts = (data?.['renewalAttempts'] || 0) + 1;
        transaction.update(ref, {
          renewalAttempts: attempts,
          lastAttempt: new Date().toISOString()
        });

        this.logger.info('Updated renewal attempts for user', {
          userId,
          attempts,
          documentPath: ref.path
        });
      });
    } catch (error: any) {
      this.logger.error('Failed to update renewal attempts', {
        userId,
        error: error.message,
        stack: error.stack,
        collection: this.collection
      });
      throw error;
    }
  }

  async getActiveWatches(userId: string): Promise<any> {
    return await this.db.collection('watchDetails').doc(userId).get();
  }

  async getWatchesForUser(userId: string): Promise<UserWatch[]> {
    const snapshot = await this.db.collection('userEmails')
      .where('userId', '==', userId)
      .get();
    return snapshot.docs.map(doc => ({
      email: doc.id,
      ...doc.data()
    } as UserWatch));
  }
}
