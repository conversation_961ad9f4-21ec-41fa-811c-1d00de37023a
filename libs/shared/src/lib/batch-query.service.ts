import { Database } from './database';
import { Logger } from './logger';

/**
 * Service for optimized batch database operations
 * Replaces N+1 query patterns with efficient batch queries
 */
export class BatchQueryService {
  private db: Database;
  private logger: Logger;

  constructor() {
    this.db = new Database();
    this.logger = new Logger();
  }

  /**
   * Batch query for multiple analysis jobs by IDs
   * Replaces individual job status queries in polling
   */
  async getMultipleJobStatuses(jobIds: string[]): Promise<Array<{ id: string; data: any }>> {
    if (jobIds.length === 0) {
      return [];
    }

    try {
      this.logger.debug('Batch querying job statuses', { jobIds, count: jobIds.length });

      // Firestore 'in' queries are limited to 10 items, so we need to batch
      const batches = this.chunkArray(jobIds, 10);
      const allResults: Array<{ id: string; data: any }> = [];

      for (const batch of batches) {
        const snapshot = await this.db
          .collection('analysisJobs')
          .where('__name__', 'in', batch.map(id => this.db.collection('analysisJobs').doc(id)))
          .get();

        const batchResults = snapshot.docs.map(doc => ({
          id: doc.id,
          data: doc.data()
        }));

        allResults.push(...batchResults);
      }

      this.logger.debug('Batch query completed', { 
        requested: jobIds.length, 
        found: allResults.length 
      });

      return allResults;
    } catch (error) {
      this.logger.error('Failed to batch query job statuses', { 
        jobIds, 
        error: error instanceof Error ? error.message : String(error) 
      });
      throw error;
    }
  }

  /**
   * Batch query for email analysis cache status
   * Optimizes the N+1 pattern in email processing
   */
  async getEmailAnalysisCacheStatus(
    clerkUserId: string, 
    monitoredEmail: string, 
    emailIds: string[],
    currentVersion: string
  ): Promise<Map<string, { exists: boolean; needsUpdate: boolean; data?: any }>> {
    if (emailIds.length === 0) {
      return new Map();
    }

    try {
      this.logger.debug('Batch querying email analysis cache', { 
        clerkUserId, 
        emailCount: emailIds.length 
      });

      // Create document IDs for batch query
      const docIds = emailIds.map(emailId => 
        `${clerkUserId}_${monitoredEmail}_${emailId}`
      );

      // Batch query in chunks of 10 (Firestore limit)
      const batches = this.chunkArray(docIds, 10);
      const results = new Map<string, { exists: boolean; needsUpdate: boolean; data?: any }>();

      for (const batch of batches) {
        const snapshot = await this.db
          .collection('emailAnalysis')
          .where('__name__', 'in', batch.map(id => this.db.collection('emailAnalysis').doc(id)))
          .get();

        // Process results
        const foundDocs = new Set(snapshot.docs.map(doc => doc.id));
        
        batch.forEach((docId, index) => {
          const emailId = emailIds[docIds.indexOf(docId)];
          const doc = snapshot.docs.find(d => d.id === docId);
          
          if (doc && doc.exists) {
            const data = doc.data();
            const needsUpdate = data?.['version'] !== currentVersion;
            results.set(emailId, {
              exists: true,
              needsUpdate,
              data: needsUpdate ? undefined : data
            });
          } else {
            results.set(emailId, {
              exists: false,
              needsUpdate: true
            });
          }
        });
      }

      this.logger.debug('Email analysis cache query completed', { 
        requested: emailIds.length,
        cached: Array.from(results.values()).filter(r => r.exists && !r.needsUpdate).length,
        needsAnalysis: Array.from(results.values()).filter(r => r.needsUpdate).length
      });

      return results;
    } catch (error) {
      this.logger.error('Failed to batch query email analysis cache', { 
        clerkUserId, 
        emailCount: emailIds.length,
        error: error instanceof Error ? error.message : String(error) 
      });
      throw error;
    }
  }

  /**
   * Batch query for analysis runs with filters
   * Optimizes the analysis runs listing page
   */
  async getAnalysisRunsBatch(
    clerkUserId: string,
    filters: {
      sourceType?: string;
      status?: string;
      limit?: number;
      offset?: number;
      sortBy?: string;
      sortOrder?: 'asc' | 'desc';
    } = {}
  ): Promise<{ runs: any[]; total: number }> {
    try {
      this.logger.debug('Batch querying analysis runs', { clerkUserId, filters });

      let query = this.db
        .collection('analysisJobs')
        .where('clerkUserId', '==', clerkUserId);

      // Apply filters
      if (filters.sourceType) {
        query = query.where('sourceType', '==', filters.sourceType);
      }

      if (filters.status) {
        query = query.where('status', '==', filters.status);
      }

      // Apply sorting
      const sortBy = filters.sortBy || 'createdAt';
      const sortOrder = filters.sortOrder || 'desc';
      query = query.orderBy(sortBy, sortOrder);

      // Get total count first (for pagination)
      const totalSnapshot = await query.get();
      const total = totalSnapshot.size;

      // Apply pagination
      if (filters.offset) {
        query = query.offset(filters.offset);
      }

      if (filters.limit) {
        query = query.limit(filters.limit);
      }

      // Execute paginated query
      const snapshot = await query.get();
      const runs = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));

      this.logger.debug('Analysis runs batch query completed', { 
        clerkUserId,
        total,
        returned: runs.length
      });

      return { runs, total };
    } catch (error) {
      this.logger.error('Failed to batch query analysis runs', { 
        clerkUserId,
        filters,
        error: error instanceof Error ? error.message : String(error) 
      });
      throw error;
    }
  }

  /**
   * Batch write operations for analysis results
   * Optimizes bulk updates and inserts
   */
  async batchWriteAnalysisResults(
    operations: Array<{
      type: 'set' | 'update' | 'delete';
      collection: string;
      docId: string;
      data?: any;
    }>
  ): Promise<void> {
    if (operations.length === 0) {
      return;
    }

    try {
      this.logger.debug('Executing batch write operations', { count: operations.length });

      // Firestore batch writes are limited to 500 operations
      const batches = this.chunkArray(operations, 500);

      for (const batch of batches) {
        const writeBatch = this.db.db.batch();

        batch.forEach(operation => {
          const docRef = this.db.collection(operation.collection).doc(operation.docId);

          switch (operation.type) {
            case 'set':
              writeBatch.set(docRef, operation.data);
              break;
            case 'update':
              writeBatch.update(docRef, operation.data);
              break;
            case 'delete':
              writeBatch.delete(docRef);
              break;
          }
        });

        await writeBatch.commit();
      }

      this.logger.debug('Batch write operations completed', { 
        totalOperations: operations.length,
        batches: batches.length
      });
    } catch (error) {
      this.logger.error('Failed to execute batch write operations', { 
        operationCount: operations.length,
        error: error instanceof Error ? error.message : String(error) 
      });
      throw error;
    }
  }

  /**
   * Utility function to chunk arrays for batch processing
   */
  private chunkArray<T>(array: T[], chunkSize: number): T[][] {
    const chunks: T[][] = [];
    for (let i = 0; i < array.length; i += chunkSize) {
      chunks.push(array.slice(i, i + chunkSize));
    }
    return chunks;
  }

  /**
   * Get performance metrics for batch operations
   */
  getPerformanceMetrics(): {
    averageQueryTime: number;
    totalQueries: number;
    cacheHitRate: number;
  } {
    // This would be implemented with actual metrics collection
    return {
      averageQueryTime: 0,
      totalQueries: 0,
      cacheHitRate: 0
    };
  }
}
