/**
 * Simple in-memory rate limiter for current usage scale
 * Note: For production scale, consider Redis-based rate limiting
 */

interface RateLimitEntry {
  count: number;
  resetTime: number;
}

interface RateLimitConfig {
  requests: number;
  windowMs: number;
}

export class RateLimiter {
  private static instance: RateLimiter;
  private limits: Map<string, RateLimitEntry> = new Map();
  private cleanupInterval: NodeJS.Timeout;

  private constructor() {
    // Clean up expired entries every 5 minutes
    this.cleanupInterval = setInterval(() => {
      this.cleanup();
    }, 5 * 60 * 1000);
  }

  static getInstance(): RateLimiter {
    if (!RateLimiter.instance) {
      RateLimiter.instance = new RateLimiter();
    }
    return RateLimiter.instance;
  }

  /**
   * Check if a request should be rate limited
   * @param key - Unique identifier (usually userId or IP)
   * @param config - Rate limit configuration
   * @returns { allowed: boolean, remaining: number, resetTime: number }
   */
  checkLimit(key: string, config: RateLimitConfig): {
    allowed: boolean;
    remaining: number;
    resetTime: number;
  } {
    const now = Date.now();
    const entry = this.limits.get(key);

    // If no entry exists or window has expired, create new entry
    if (!entry || now > entry.resetTime) {
      const resetTime = now + config.windowMs;
      this.limits.set(key, { count: 1, resetTime });
      return {
        allowed: true,
        remaining: config.requests - 1,
        resetTime
      };
    }

    // Check if limit exceeded
    if (entry.count >= config.requests) {
      return {
        allowed: false,
        remaining: 0,
        resetTime: entry.resetTime
      };
    }

    // Increment count
    entry.count++;
    return {
      allowed: true,
      remaining: config.requests - entry.count,
      resetTime: entry.resetTime
    };
  }

  /**
   * Clean up expired entries to prevent memory leaks
   */
  private cleanup(): void {
    const now = Date.now();
    for (const [key, entry] of this.limits.entries()) {
      if (now > entry.resetTime) {
        this.limits.delete(key);
      }
    }
  }

  /**
   * Get current stats (for monitoring)
   */
  getStats(): { totalKeys: number; memoryUsage: string } {
    return {
      totalKeys: this.limits.size,
      memoryUsage: `${Math.round(this.limits.size * 64 / 1024)}KB` // Rough estimate
    };
  }

  /**
   * Clear all rate limit data (for testing)
   */
  clear(): void {
    this.limits.clear();
  }

  /**
   * Destroy the rate limiter and cleanup
   */
  destroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
    this.limits.clear();
  }
}

// Rate limit configurations for different endpoints
export const RATE_LIMITS = {
  // General API endpoints
  general: { requests: 100, windowMs: 60 * 1000 }, // 100 requests per minute
  
  // Analysis endpoints (more restrictive)
  analysis: { requests: 10, windowMs: 60 * 1000 }, // 10 requests per minute
  
  // Metrics endpoints
  metrics: { requests: 60, windowMs: 60 * 1000 }, // 60 requests per minute
  
  // Authentication endpoints
  auth: { requests: 20, windowMs: 60 * 1000 }, // 20 requests per minute
} as const;

/**
 * Middleware helper for Next.js API routes
 */
export function createRateLimitMiddleware(config: RateLimitConfig) {
  const rateLimiter = RateLimiter.getInstance();

  return function rateLimitMiddleware(userId: string): {
    allowed: boolean;
    headers: Record<string, string>;
  } {
    const result = rateLimiter.checkLimit(userId, config);
    
    const headers: Record<string, string> = {
      'X-RateLimit-Limit': config.requests.toString(),
      'X-RateLimit-Remaining': result.remaining.toString(),
      'X-RateLimit-Reset': Math.ceil(result.resetTime / 1000).toString(),
    };

    if (!result.allowed) {
      headers['Retry-After'] = Math.ceil((result.resetTime - Date.now()) / 1000).toString();
    }

    return {
      allowed: result.allowed,
      headers
    };
  };
}

// Export singleton instance for convenience
export const rateLimiter = RateLimiter.getInstance();
