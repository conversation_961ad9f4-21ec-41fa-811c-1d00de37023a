/**
 * Centralized environment variable management service
 * Provides type-safe access to environment variables with validation and defaults
 */

export interface EnvironmentConfig {
  // Application
  nodeEnv: 'development' | 'production' | 'test'
  port: number
  
  // Database
  projectId: string
  firestoreEmulatorHost?: string
  useFirestoreEmulator: boolean
  
  // Authentication
  clerkSecretKey: string
  clerkPublishableKey: string
  clerkJwtKey?: string
  
  // External APIs
  openaiApiKey: string
  
  // GCP
  gcpProjectId: string
  gcpRegion: string
  gcpServiceAccountKey?: string
  
  // Pub/Sub
  pubsubTopicName: string
  pubsubSubscriptionName: string
  
  // Security
  jwtSecret?: string
  corsOrigins: string[]
  
  // Features
  enableRateLimiting: boolean
  enableSecurityHeaders: boolean
  enableAnalytics: boolean
  
  // Performance
  maxEmailsPerBatch: number
  pollingIntervalMs: number
  cacheTimeoutMs: number
}

export class EnvironmentService {
  private static instance: EnvironmentService
  private config: EnvironmentConfig

  private constructor() {
    this.config = this.loadConfiguration()
    this.validateConfiguration()
  }

  static getInstance(): EnvironmentService {
    if (!EnvironmentService.instance) {
      EnvironmentService.instance = new EnvironmentService()
    }
    return EnvironmentService.instance
  }

  /**
   * Get the complete environment configuration
   */
  getConfig(): EnvironmentConfig {
    return { ...this.config }
  }

  /**
   * Get a specific environment variable with type safety
   */
  get<K extends keyof EnvironmentConfig>(key: K): EnvironmentConfig[K] {
    return this.config[key]
  }

  /**
   * Check if running in development mode
   */
  isDevelopment(): boolean {
    return this.config.nodeEnv === 'development'
  }

  /**
   * Check if running in production mode
   */
  isProduction(): boolean {
    return this.config.nodeEnv === 'production'
  }

  /**
   * Check if running in test mode
   */
  isTest(): boolean {
    return this.config.nodeEnv === 'test'
  }

  /**
   * Get database configuration
   */
  getDatabaseConfig() {
    return {
      projectId: this.config.projectId,
      useEmulator: this.config.useFirestoreEmulator,
      emulatorHost: this.config.firestoreEmulatorHost
    }
  }

  /**
   * Get authentication configuration
   */
  getAuthConfig() {
    return {
      clerkSecretKey: this.config.clerkSecretKey,
      clerkPublishableKey: this.config.clerkPublishableKey,
      clerkJwtKey: this.config.clerkJwtKey,
      jwtSecret: this.config.jwtSecret
    }
  }

  /**
   * Get GCP configuration
   */
  getGcpConfig() {
    return {
      projectId: this.config.gcpProjectId,
      region: this.config.gcpRegion,
      serviceAccountKey: this.config.gcpServiceAccountKey
    }
  }

  /**
   * Get security configuration
   */
  getSecurityConfig() {
    return {
      enableRateLimiting: this.config.enableRateLimiting,
      enableSecurityHeaders: this.config.enableSecurityHeaders,
      corsOrigins: this.config.corsOrigins
    }
  }

  /**
   * Get performance configuration
   */
  getPerformanceConfig() {
    return {
      maxEmailsPerBatch: this.config.maxEmailsPerBatch,
      pollingIntervalMs: this.config.pollingIntervalMs,
      cacheTimeoutMs: this.config.cacheTimeoutMs
    }
  }

  /**
   * Load configuration from environment variables
   */
  private loadConfiguration(): EnvironmentConfig {
    return {
      // Application
      nodeEnv: (process.env['NODE_ENV'] as any) || 'development',
      port: parseInt(process.env['PORT'] || '3000', 10),
      
      // Database
      projectId: process.env['GOOGLE_CLOUD_PROJECT'] || process.env['GCP_PROJECT_ID'] || 'ddjs-dev-458016',
      firestoreEmulatorHost: process.env['FIRESTORE_EMULATOR_HOST'],
      useFirestoreEmulator: !!process.env['FIRESTORE_EMULATOR_HOST'],
      
      // Authentication
      clerkSecretKey: process.env['CLERK_SECRET_KEY'] || '',
      clerkPublishableKey: process.env['NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY'] || '',
      clerkJwtKey: process.env['CLERK_JWT_KEY'],
      
      // External APIs
      openaiApiKey: process.env['OPENAI_API_KEY'] || '',

      // GCP
      gcpProjectId: process.env['GOOGLE_CLOUD_PROJECT'] || process.env['GCP_PROJECT_ID'] || 'ddjs-dev-458016',
      gcpRegion: process.env['GCP_REGION'] || 'us-central1',
      gcpServiceAccountKey: process.env['GOOGLE_APPLICATION_CREDENTIALS'],
      
      // Pub/Sub
      pubsubTopicName: process.env['PUBSUB_TOPIC_NAME'] || 'email-analysis-requests',
      pubsubSubscriptionName: process.env['PUBSUB_SUBSCRIPTION_NAME'] || 'email-analysis-subscription',

      // Security
      jwtSecret: process.env['JWT_SECRET'],
      corsOrigins: this.parseCorsOrigins(process.env['CORS_ORIGINS']),
      
      // Features
      enableRateLimiting: process.env['ENABLE_RATE_LIMITING'] !== 'false',
      enableSecurityHeaders: process.env['ENABLE_SECURITY_HEADERS'] !== 'false',
      enableAnalytics: process.env['ENABLE_ANALYTICS'] === 'true',

      // Performance
      maxEmailsPerBatch: parseInt(process.env['MAX_EMAILS_PER_BATCH'] || '100', 10),
      pollingIntervalMs: parseInt(process.env['POLLING_INTERVAL_MS'] || '5000', 10),
      cacheTimeoutMs: parseInt(process.env['CACHE_TIMEOUT_MS'] || '300000', 10), // 5 minutes
    }
  }

  /**
   * Parse CORS origins from environment variable
   */
  private parseCorsOrigins(corsOriginsEnv?: string): string[] {
    if (!corsOriginsEnv) {
      return this.getDefaultCorsOrigins()
    }
    
    return corsOriginsEnv.split(',').map(origin => origin.trim()).filter(Boolean)
  }

  /**
   * Get default CORS origins based on environment
   */
  private getDefaultCorsOrigins(): string[] {
    const nodeEnv = process.env['NODE_ENV'] || 'development'
    
    switch (nodeEnv) {
      case 'production':
        return ['https://datadrivenjobsearch.com']
      case 'development':
        return [
          'http://localhost:3000',
          'http://localhost:4200',
          'https://dev.datadrivenjobsearch.com'
        ]
      case 'test':
        return ['http://localhost:3000']
      default:
        return ['http://localhost:3000']
    }
  }

  /**
   * Validate required environment variables
   */
  private validateConfiguration(): void {
    const errors: string[] = []

    // Required in all environments
    if (!this.config.clerkSecretKey) {
      errors.push('CLERK_SECRET_KEY is required')
    }

    if (!this.config.clerkPublishableKey) {
      errors.push('NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY is required')
    }

    // Required in production runtime (not during build)
    if (this.isProduction() && typeof window === 'undefined' && process.env['NEXT_PHASE'] !== 'phase-production-build') {
      if (!this.config.openaiApiKey) {
        errors.push('OPENAI_API_KEY is required in production')
      }

      if (!this.config.gcpServiceAccountKey && !process.env['GOOGLE_APPLICATION_CREDENTIALS']) {
        errors.push('GOOGLE_APPLICATION_CREDENTIALS is required in production')
      }
    }

    // Validate numeric values
    if (this.config.port < 1 || this.config.port > 65535) {
      errors.push('PORT must be between 1 and 65535')
    }

    if (this.config.maxEmailsPerBatch < 1 || this.config.maxEmailsPerBatch > 1000) {
      errors.push('MAX_EMAILS_PER_BATCH must be between 1 and 1000')
    }

    if (this.config.pollingIntervalMs < 1000 || this.config.pollingIntervalMs > 60000) {
      errors.push('POLLING_INTERVAL_MS must be between 1000 and 60000')
    }

    if (errors.length > 0) {
      throw new Error(`Environment configuration errors:\n${errors.join('\n')}`)
    }
  }

  /**
   * Get environment variables for deployment
   */
  getDeploymentEnvVars(): Record<string, string> {
    return {
      NODE_ENV: this.config.nodeEnv,
      PORT: this.config.port.toString(),
      GOOGLE_CLOUD_PROJECT: this.config.gcpProjectId,
      GCP_REGION: this.config.gcpRegion,
      PUBSUB_TOPIC_NAME: this.config.pubsubTopicName,
      PUBSUB_SUBSCRIPTION_NAME: this.config.pubsubSubscriptionName,
      MAX_EMAILS_PER_BATCH: this.config.maxEmailsPerBatch.toString(),
      POLLING_INTERVAL_MS: this.config.pollingIntervalMs.toString(),
      CACHE_TIMEOUT_MS: this.config.cacheTimeoutMs.toString(),
      ENABLE_RATE_LIMITING: this.config.enableRateLimiting.toString(),
      ENABLE_SECURITY_HEADERS: this.config.enableSecurityHeaders.toString(),
      CORS_ORIGINS: this.config.corsOrigins.join(',')
    }
  }

  /**
   * Log configuration (without sensitive data)
   */
  logConfiguration(): void {
    const safeConfig = {
      nodeEnv: this.config.nodeEnv,
      port: this.config.port,
      projectId: this.config.projectId,
      useFirestoreEmulator: this.config.useFirestoreEmulator,
      gcpProjectId: this.config.gcpProjectId,
      gcpRegion: this.config.gcpRegion,
      enableRateLimiting: this.config.enableRateLimiting,
      enableSecurityHeaders: this.config.enableSecurityHeaders,
      enableAnalytics: this.config.enableAnalytics,
      maxEmailsPerBatch: this.config.maxEmailsPerBatch,
      pollingIntervalMs: this.config.pollingIntervalMs,
      cacheTimeoutMs: this.config.cacheTimeoutMs,
      corsOrigins: this.config.corsOrigins,
      // Sensitive data masked
      clerkSecretKey: this.config.clerkSecretKey ? '***' : 'NOT_SET',
      openaiApiKey: this.config.openaiApiKey ? '***' : 'NOT_SET',
      gcpServiceAccountKey: this.config.gcpServiceAccountKey ? '***' : 'NOT_SET'
    }

    console.log('Environment Configuration:', JSON.stringify(safeConfig, null, 2))
  }
}

// Export singleton instance
export const environmentService = EnvironmentService.getInstance()
